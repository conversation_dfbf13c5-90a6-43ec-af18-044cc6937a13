# Database Setup for PawLedger

## Prerequisites
- PostgreSQL installed and running
- Access to PostgreSQL as a superuser (usually `postgres` user)

## Setup Instructions

### Option 1: Using the provided SQL script
1. Run the setup script as the postgres superuser:
```bash
sudo -u postgres psql -f setup-database.sql
```

### Option 2: Manual setup
1. Connect to PostgreSQL as superuser:
```bash
sudo -u postgres psql
```

2. Create the database and user:
```sql
CREATE DATABASE pawledger;
CREATE USER web_user WITH PASSWORD 'P$Nvv&d.#fjs(Io01';
GRANT ALL PRIVILEGES ON DATABASE pawledger TO web_user;
\c pawledger;
GRANT ALL ON SCHEMA public TO web_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO web_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO web_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO web_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO web_user;
\q
```

### Option 3: Use default postgres user (for development)
If you prefer to use the default postgres user, update your `.env.local` file:
```
POSTGRES_DB=pawledger
POSTGRES_HOST=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_PORT=5432
```

## Verification
After setup, you can test the connection:
```bash
psql -h localhost -U web_user -d pawledger
```

## Application Usage
Once the database is set up, the application will automatically create the necessary tables when you first register a user.

## Troubleshooting
- If you get "password authentication failed", check that the user exists and the password is correct
- If you get "database does not exist", make sure you created the database
- If you get permission errors, ensure the user has proper privileges on the database and schema
