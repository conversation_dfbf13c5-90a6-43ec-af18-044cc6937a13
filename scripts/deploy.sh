#!/bin/bash

# PawLedger Deployment Script for Vercel
echo "🐾 PawLedger Deployment Script"
echo "=============================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel
fi

# Check if user is logged in to Vercel
echo "🔐 Checking Vercel authentication..."
if ! vercel whoami &> /dev/null; then
    echo "🔑 Please log in to Vercel:"
    vercel login
fi

# Run build test locally
echo "🔨 Testing build locally..."
if ! npm run build; then
    echo "❌ Build failed locally. Please fix errors before deploying."
    exit 1
fi

echo "✅ Local build successful!"

# Check for environment variables
echo "🔍 Checking for required environment variables..."
if [ ! -f ".env.local" ]; then
    echo "⚠️  Warning: .env.local not found. Make sure to configure environment variables in Vercel dashboard."
fi

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
echo ""
echo "📝 Deployment Notes:"
echo "   - This will create a preview deployment"
echo "   - Use 'vercel --prod' for production deployment"
echo "   - Configure environment variables in Vercel dashboard"
echo ""

read -p "Continue with deployment? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    vercel
    
    echo ""
    echo "✅ Deployment initiated!"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Configure environment variables in Vercel dashboard"
    echo "   2. Set up Vercel Postgres database"
    echo "   3. Test the deployed application"
    echo "   4. Configure custom domain (optional)"
    echo ""
    echo "🔗 Useful Links:"
    echo "   - Vercel Dashboard: https://vercel.com/dashboard"
    echo "   - Deployment Guide: ./deploy-to-vercel.md"
    echo ""
else
    echo "❌ Deployment cancelled."
fi
