// Script to check current breeders
const { Pool } = require('pg')

async function checkBreeders() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'pawledger',
    user: 'web_user',
    password: 'simplepass123'
  })

  try {
    console.log('Checking current breeders...')
    
    const breeders = await pool.query('SELECT id, user_id, business_name, breeder_code, logo_url FROM breeders ORDER BY id')
    console.log(`Found ${breeders.rows.length} breeders:`)
    breeders.rows.forEach(breeder => {
      console.log(`  ID: ${breeder.id}, User ID: ${breeder.user_id}, Business: ${breeder.business_name}, Code: ${breeder.breeder_code}, Logo: ${breeder.logo_url || 'None'}`)
    })

  } catch (error) {
    console.error('Error:', error.message)
  } finally {
    await pool.end()
  }
}

checkBreeders().catch(console.error)
