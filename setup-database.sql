-- Database setup script for Paw<PERSON>edger
-- Run this as a PostgreSQL superuser (e.g., postgres user)

-- Create database
CREATE DATABASE pawledger;

-- Create user
CREATE USER web_user WITH PASSWORD 'P$Nvv&d.#fjs(Io01';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE pawledger TO web_user;

-- Connect to the database
\c pawledger;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO web_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO web_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO web_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO web_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO web_user;
