// Script to create sample data for the public website
const { Pool } = require('pg')

async function createSampleData() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'pawledger',
    user: 'web_user',
    password: 'simplepass123'
  })

  try {
    console.log('Creating sample data for Happy Paws Kennel...')

    // Get the breeder ID
    const breederResult = await pool.query(
      'SELECT id FROM users WHERE breeder_code = $1',
      ['happy-paws-kennel']
    )

    if (breederResult.rows.length === 0) {
      console.log('Breeder not found!')
      return
    }

    const breederId = breederResult.rows[0].id
    console.log(`Found breeder ID: ${breederId}`)

    // Create a sample litter
    const litterResult = await pool.query(`
      INSERT INTO litters (litter_code, min_expected_size_lbs, max_expected_size_lbs, 
                          expected_birth_date, actual_birth_date, status, breeder_id, color)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (litter_code) DO NOTHING
      RETURNING id
    `, [
      'HPK-2024-SPRING',
      8,
      12,
      '2024-03-15',
      '2024-03-18',
      'READY_TO_GO_HOME',
      breederId,
      'Golden'
    ])

    let litterId
    if (litterResult.rows.length > 0) {
      litterId = litterResult.rows[0].id
      console.log(`Created litter with ID: ${litterId}`)
    } else {
      // Litter already exists, get its ID
      const existingLitter = await pool.query(
        'SELECT id FROM litters WHERE litter_code = $1',
        ['HPK-2024-SPRING']
      )
      litterId = existingLitter.rows[0].id
      console.log(`Using existing litter ID: ${litterId}`)
    }

    // Create sample puppies
    const puppies = [
      { name: 'Buddy', description: 'Friendly and energetic male puppy with beautiful golden coat', color: 'Golden', price: 1500, available: true },
      { name: 'Luna', description: 'Sweet and gentle female puppy, great with children', color: 'Light Golden', price: 1600, available: true },
      { name: 'Max', description: 'Playful male puppy with excellent temperament', color: 'Golden', price: 1500, available: false },
      { name: 'Bella', description: 'Intelligent female puppy, quick learner', color: 'Dark Golden', price: 1650, available: true },
      { name: 'Charlie', description: 'Calm and loving male puppy, perfect family dog', color: 'Golden', price: 1550, available: false }
    ]

    for (const puppy of puppies) {
      try {
        const result = await pool.query(`
          INSERT INTO puppies (name, description, birth_date, color, price, litter_id, 
                              owner_id, owner_type)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          ON CONFLICT DO NOTHING
        `, [
          puppy.name,
          puppy.description,
          '2024-03-18',
          puppy.color,
          puppy.price * 100, // Store as cents
          litterId,
          breederId,
          puppy.available ? 'breeder' : 'customer'
        ])
        console.log(`Created puppy: ${puppy.name}`)
      } catch (error) {
        console.log(`Puppy ${puppy.name} might already exist`)
      }
    }

    // Create another litter (upcoming)
    try {
      const upcomingLitterResult = await pool.query(`
        INSERT INTO litters (litter_code, min_expected_size_lbs, max_expected_size_lbs, 
                            expected_birth_date, status, breeder_id, color)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (litter_code) DO NOTHING
        RETURNING id
      `, [
        'HPK-2024-SUMMER',
        10,
        14,
        '2024-08-15',
        'NOT_BORN',
        breederId,
        'Cream'
      ])

      if (upcomingLitterResult.rows.length > 0) {
        console.log(`Created upcoming litter: HPK-2024-SUMMER`)
      }
    } catch (error) {
      console.log('Upcoming litter might already exist')
    }

    console.log('Sample data creation completed!')
    console.log(`Visit the public website at: http://localhost:3001/happy-paws-kennel`)

  } catch (error) {
    console.error('Error creating sample data:', error.message)
  } finally {
    await pool.end()
  }
}

createSampleData().catch(console.error)
