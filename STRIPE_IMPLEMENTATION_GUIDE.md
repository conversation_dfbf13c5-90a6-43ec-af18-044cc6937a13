# 🚀 PawLedger Stripe Implementation Guide

## Overview
This guide implements your updated subscription plans with Stripe integration:

- **Starter**: $19/month - 2 litters, 20 puppies, 50 customers, 30 photos/docs
- **Professional**: $49/month - 10 litters, 100 puppies, 150 customers, 200 photos/docs  
- **Premium**: $99/month - Unlimited litters/puppies/customers, 1000 photos/docs

## 📋 Implementation Checklist

### 1. Database Setup ✅
- [x] Added subscription tables to database schema
- [x] Created migration for subscription_plans and subscriptions tables
- [x] Updated Database interface with new tables

### 2. Stripe Configuration ✅
- [x] Created Stripe utility functions (`src/lib/stripe.ts`)
- [x] Defined subscription plans with limits and features
- [x] Added helper functions for customer management and checkout

### 3. API Routes ✅
- [x] Webhook handler (`/api/webhooks/stripe`)
- [x] Checkout session creation (`/api/subscriptions/checkout`)
- [x] Current subscription retrieval (`/api/subscriptions/current`)

### 4. Frontend Components ✅
- [x] Subscription management component (`SubscriptionManager.tsx`)
- [x] Usage tracking and limit visualization
- [x] Plan upgrade/downgrade interface

## 🔧 Setup Instructions

### Step 1: Install Stripe Package
```bash
npm install stripe @stripe/stripe-js
npm install --save-dev @types/stripe
```

### Step 2: Environment Variables
Your `.env.local` already has the Stripe keys configured:
```env
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Step 3: Create Stripe Products and Prices
Run this script to create products in your Stripe dashboard:

```typescript
// src/scripts/create-stripe-products.ts
import { createStripeProducts } from '@/lib/stripe'

async function main() {
  const products = await createStripeProducts()
  console.log('Created products:', products)
  
  // Update your subscription_plans table with the actual Stripe IDs
  for (const product of products) {
    await db
      .updateTable('subscription_plans')
      .set({
        stripe_product_id: product.product.id,
        stripe_price_id: product.monthlyPrice.id, // Store monthly by default
      })
      .where('name', '=', product.planType)
      .execute()
  }
}

main()
```

### Step 4: Database Migration
Run the migration to create subscription tables:
```bash
# This will create the subscription_plans and subscriptions tables
npm run migrate
```

### Step 5: Seed Subscription Plans
```bash
npx tsx src/scripts/seed-subscription-plans.ts
```

### Step 6: Configure Webhook Endpoint
1. In Stripe Dashboard → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### Step 7: Add Subscription Manager to Settings Page
Update `src/app/breeder/settings/page.tsx`:

```typescript
import { SubscriptionManager } from '@/components/subscriptions/SubscriptionManager'

// Add this section to your settings page
<div className="mb-8">
  <SubscriptionManager />
</div>
```

## 🔒 Usage Limit Enforcement

### Middleware for Limit Checking
Add this to your API routes to enforce limits:

```typescript
import { checkPlanLimits } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  const session = await verifySession(request)
  const limits = await checkPlanLimits(session.userId)
  
  // Example: Check litter limit before creating new litter
  if (limits && limits.limits.max_litters !== null) {
    if (limits.usage.litters >= limits.limits.max_litters) {
      return NextResponse.json(
        { error: 'Litter limit reached. Please upgrade your plan.' },
        { status: 403 }
      )
    }
  }
  
  // Continue with creation...
}
```

### Frontend Limit Display
The `SubscriptionManager` component automatically shows:
- Current usage vs limits
- Color-coded progress bars (green/yellow/red)
- Upgrade prompts when approaching limits

## 💳 Payment Flow

### 1. User Selects Plan
- User clicks "Start Trial" or "Upgrade" button
- Frontend calls `/api/subscriptions/checkout`

### 2. Stripe Checkout
- Redirects to Stripe-hosted checkout page
- 14-day free trial included
- Collects payment method

### 3. Webhook Processing
- Stripe sends webhook to `/api/webhooks/stripe`
- Creates subscription record in database
- Updates user's plan and limits

### 4. Success Redirect
- User returns to settings page
- Subscription status updated
- New limits immediately available

## 🎯 Key Features Implemented

### ✅ Free Trial
- 14-day trial for all plans
- No credit card required upfront
- Full access to plan features during trial

### ✅ Usage Tracking
- Real-time limit monitoring
- Visual progress indicators
- Automatic enforcement in API routes

### ✅ Plan Management
- Easy upgrade/downgrade
- Prorated billing
- Immediate plan changes

### ✅ Billing Integration
- Automatic recurring billing
- Failed payment handling
- Subscription status sync

## 🚨 Important Notes

### Security
- All Stripe operations use server-side API
- Webhook signature verification implemented
- User authentication required for all subscription operations

### Error Handling
- Graceful degradation for payment failures
- Clear error messages for limit violations
- Automatic retry for failed webhooks

### Testing
- Use Stripe test mode for development
- Test webhook endpoints with Stripe CLI
- Verify limit enforcement before production

## 📊 Analytics & Monitoring

### Subscription Metrics
Track these KPIs in your admin dashboard:
- Monthly Recurring Revenue (MRR)
- Churn rate by plan
- Trial-to-paid conversion rate
- Average revenue per user (ARPU)

### Usage Analytics
- Feature adoption by plan
- Limit utilization rates
- Upgrade trigger points

## 🔄 Next Steps

1. **Test the complete flow** in Stripe test mode
2. **Create actual Stripe products** and update price IDs
3. **Configure webhook endpoint** in production
4. **Add subscription management** to settings page
5. **Implement usage limit checks** in all relevant API routes
6. **Set up monitoring** for subscription events
7. **Create admin dashboard** for subscription analytics

## 🆘 Troubleshooting

### Common Issues
- **Webhook not receiving events**: Check endpoint URL and selected events
- **Price ID not found**: Ensure Stripe products are created and IDs updated
- **Limits not enforcing**: Verify checkPlanLimits is called in API routes
- **Trial not starting**: Check trial_period_days in checkout session

### Debug Tools
- Stripe Dashboard → Events (view all webhook events)
- Stripe CLI for local webhook testing
- Database queries to verify subscription status
- Browser network tab for API call debugging

Your Stripe integration is now ready for production! 🎉
