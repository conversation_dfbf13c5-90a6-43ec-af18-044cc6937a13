# PawLedger Vercel Deployment Guide

## 🚀 Quick Deployment Steps

### 1. Prerequisites
- [ ] Git repository with your code
- [ ] Vercel account (free tier available)
- [ ] Google Cloud Storage bucket set up
- [ ] Gmail app password for SMTP

### 2. Database Setup (Vercel Postgres)

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Create Vercel Postgres database
vercel storage create postgres --name pawledger-db
```

### 3. Environment Variables for Vercel

Add these in your Vercel project dashboard (Settings → Environment Variables):

```bash
# Database (from Vercel Postgres)
POSTGRES_DB=vercel-postgres-db-name
POSTGRES_HOST=your-postgres-host.vercel-storage.com
POSTGRES_USER=default
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_PORT=5432

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID=pawledger
GOOGLE_CLOUD_STORAGE_BUCKET=pawledger-images
GOOGLE_CLOUD_KEY_FILE_BASE64=your_base64_encoded_service_account_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password
FROM_EMAIL=<EMAIL>

# Security
NEXTAUTH_SECRET=your-random-32-character-secret
NEXTAUTH_URL=https://your-app-name.vercel.app
```

### 4. Convert Google Cloud Service Account Key

```bash
# Convert your service account JSON to base64
base64 -i /path/to/your/service-account-key.json | tr -d '\n'
```

### 5. Deploy Commands

```bash
# Deploy to Vercel
vercel

# Or deploy to production directly
vercel --prod
```

### 6. Post-Deployment Setup

1. **Database Migration**: Run your database setup scripts
2. **Test Upload**: Verify image uploads work
3. **Test Email**: Verify email functionality
4. **Custom Domain**: Add your custom domain in Vercel dashboard

### 7. Monitoring & Maintenance

- **Logs**: Check Vercel function logs for errors
- **Analytics**: Monitor performance in Vercel dashboard
- **Database**: Monitor Vercel Postgres usage
- **Storage**: Monitor Google Cloud Storage usage

## 🔧 Troubleshooting

### Common Issues:

1. **Build Errors**: Check TypeScript errors and dependencies
2. **Database Connection**: Verify Postgres environment variables
3. **Image Uploads**: Check Google Cloud credentials and bucket permissions
4. **Email Issues**: Verify Gmail app password and SMTP settings

### Build Optimization:

```bash
# Test build locally before deploying
npm run build

# Check for any build warnings or errors
npm run lint
```

## 📊 Production Checklist

- [ ] Environment variables configured
- [ ] Database connected and migrated
- [ ] Image uploads working
- [ ] Email notifications working
- [ ] Custom domain configured (optional)
- [ ] SSL certificate active
- [ ] Performance monitoring set up
- [ ] Backup strategy in place

## 🌐 URLs After Deployment

- **Production**: https://your-app-name.vercel.app
- **Admin Dashboard**: https://your-app-name.vercel.app/breeder/dashboard
- **Public Websites**: https://your-app-name.vercel.app/{breederCode}

## 💡 Pro Tips

1. **Use Preview Deployments**: Every git push creates a preview deployment
2. **Environment Variables**: Use different values for preview vs production
3. **Database**: Consider using connection pooling for better performance
4. **Images**: Optimize images before upload for better performance
5. **Monitoring**: Set up Vercel Analytics for insights

## 🔒 Security Considerations

- Never commit `.env` files to git
- Use strong passwords for database and email
- Regularly rotate API keys and secrets
- Enable 2FA on all service accounts
- Monitor access logs regularly

## 📈 Scaling Considerations

- **Database**: Monitor connection limits
- **Storage**: Monitor bandwidth and storage usage
- **Functions**: Be aware of Vercel function limits
- **Email**: Consider upgrading email service for high volume
