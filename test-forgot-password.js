// Test script for forgot password functionality
const baseUrl = 'http://localhost:3000'

async function testForgotPassword() {
  console.log('Testing forgot password functionality...')
  
  // Test 1: Send forgot password request
  console.log('\n1. Testing forgot password request...')
  try {
    const response = await fetch(`${baseUrl}/api/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: '<EMAIL>' }),
    })
    
    const data = await response.json()
    console.log('Response:', data)
    console.log('Status:', response.status)
  } catch (error) {
    console.error('Error:', error.message)
  }
  
  // Test 2: Test with invalid email
  console.log('\n2. Testing with invalid email...')
  try {
    const response = await fetch(`${baseUrl}/api/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: 'invalid-email' }),
    })
    
    const data = await response.json()
    console.log('Response:', data)
    console.log('Status:', response.status)
  } catch (error) {
    console.error('Error:', error.message)
  }
  
  // Test 3: Test with missing email
  console.log('\n3. Testing with missing email...')
  try {
    const response = await fetch(`${baseUrl}/api/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    })
    
    const data = await response.json()
    console.log('Response:', data)
    console.log('Status:', response.status)
  } catch (error) {
    console.error('Error:', error.message)
  }
  
  // Test 4: Test reset password with invalid token
  console.log('\n4. Testing reset password with invalid token...')
  try {
    const response = await fetch(`${baseUrl}/api/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        token: 'invalid-token',
        password: 'newpassword123'
      }),
    })
    
    const data = await response.json()
    console.log('Response:', data)
    console.log('Status:', response.status)
  } catch (error) {
    console.error('Error:', error.message)
  }
}

// Run the test
testForgotPassword().catch(console.error)
