{"name": "pawledger", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@stripe/stripe-js": "^7.5.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "kysely": "^0.28.2", "next": "15.3.4", "nodemailer": "^7.0.5", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/stripe": "^8.0.416", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}