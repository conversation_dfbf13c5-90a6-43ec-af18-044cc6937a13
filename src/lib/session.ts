import { cookies } from 'next/headers'
import { User } from './auth'

const SESSION_COOKIE_NAME = 'pawledger_session'

export interface SessionData {
  userId: number
  userType: 'customer' | 'breeder'
  email: string
}

// Create session (simplified - in production, use proper JWT or session store)
export async function createSession(user: User): Promise<void> {
  const sessionData: SessionData = {
    userId: user.id,
    userType: user.userType,
    email: user.email,
  }

  const cookieStore = await cookies()
  cookieStore.set(SESSION_COOKIE_NAME, JSON.stringify(sessionData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  })
}

// Get session
export async function getSession(): Promise<SessionData | null> {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME)
    
    if (!sessionCookie?.value) {
      return null
    }

    const sessionData = JSON.parse(sessionCookie.value) as SessionData
    return sessionData
  } catch (error) {
    console.error('Session parsing error:', error)
    return null
  }
}

// Clear session
export async function clearSession(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete(SESSION_COOKIE_NAME)
}

// Check if user is authenticated
export async function isAuthenticated(): Promise<boolean> {
  const session = await getSession()
  return session !== null
}

// Check if user is a breeder
export async function isBreeder(): Promise<boolean> {
  const session = await getSession()
  return session?.userType === 'breeder'
}

// Check if user is a customer
export async function isCustomer(): Promise<boolean> {
  const session = await getSession()
  return session?.userType === 'customer'
}
