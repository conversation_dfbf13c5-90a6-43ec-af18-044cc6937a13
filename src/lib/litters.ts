import { db } from './database'

export interface CreateLitterData {
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  breederId: number
  color?: string
  momId?: number
  dadId?: number
}

export interface UpdateLitterData {
  litterCode?: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status?: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color?: string
  momId?: number
  dadId?: number
}

export interface Litter {
  id: number
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  breederId: number
  color?: string
  momId?: number
  dadId?: number
  createdAt: Date
  updatedAt: Date
}

// Create a new litter
export async function createLitter(data: CreateLitterData): Promise<Litter> {
  try {
    const result = await db
      .insertInto('litters')
      .values({
        litter_code: data.litterCode,
        min_expected_size_lbs: data.minExpectedSizeLbs || null,
        max_expected_size_lbs: data.maxExpectedSizeLbs || null,
        expected_birth_date: data.expectedBirthDate || null,
        actual_birth_date: data.actualBirthDate || null,
        status: data.status,
        breeder_id: data.breederId,
        color: data.color || null,
        mom_id: data.momId || null,
        dad_id: data.dadId || null,
      })
      .returning([
        'id',
        'litter_code',
        'min_expected_size_lbs',
        'max_expected_size_lbs',
        'expected_birth_date',
        'actual_birth_date',
        'status',
        'breeder_id',
        'color',
        'mom_id',
        'dad_id',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return {
      id: result.id,
      litterCode: result.litter_code,
      minExpectedSizeLbs: result.min_expected_size_lbs ?? undefined,
      maxExpectedSizeLbs: result.max_expected_size_lbs ?? undefined,
      expectedBirthDate: result.expected_birth_date ?? undefined,
      actualBirthDate: result.actual_birth_date ?? undefined,
      status: result.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
      breederId: result.breeder_id,
      color: result.color ?? undefined,
      momId: result.mom_id ?? undefined,
      dadId: result.dad_id ?? undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new Error('Litter code already exists')
    }
    throw error
  }
}

// Get litter by ID
export async function getLitterById(id: number): Promise<Litter | null> {
  try {
    const result = await db
      .selectFrom('litters')
      .select([
        'id',
        'litter_code',
        'min_expected_size_lbs',
        'max_expected_size_lbs',
        'expected_birth_date',
        'actual_birth_date',
        'status',
        'breeder_id',
        'color',
        'mom_id',
        'dad_id',
        'created_at',
        'updated_at'
      ])
      .where('id', '=', id)
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      litterCode: result.litter_code,
      minExpectedSizeLbs: result.min_expected_size_lbs ?? undefined,
      maxExpectedSizeLbs: result.max_expected_size_lbs ?? undefined,
      expectedBirthDate: result.expected_birth_date ?? undefined,
      actualBirthDate: result.actual_birth_date ?? undefined,
      status: result.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
      breederId: result.breeder_id,
      color: result.color ?? undefined,
      momId: result.mom_id ?? undefined,
      dadId: result.dad_id ?? undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Get litter error:', error)
    return null
  }
}

// Get litters by breeder ID
export async function getLittersByBreederId(breederId: number): Promise<Litter[]> {
  try {
    const results = await db
      .selectFrom('litters')
      .select([
        'id',
        'litter_code',
        'min_expected_size_lbs',
        'max_expected_size_lbs',
        'expected_birth_date',
        'actual_birth_date',
        'status',
        'breeder_id',
        'color',
        'mom_id',
        'dad_id',
        'created_at',
        'updated_at'
      ])
      .where('breeder_id', '=', breederId)
      .orderBy('created_at', 'desc')
      .execute()

    return results.map(result => ({
      id: result.id,
      litterCode: result.litter_code,
      minExpectedSizeLbs: result.min_expected_size_lbs ?? undefined,
      maxExpectedSizeLbs: result.max_expected_size_lbs ?? undefined,
      expectedBirthDate: result.expected_birth_date ?? undefined,
      actualBirthDate: result.actual_birth_date ?? undefined,
      status: result.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
      breederId: result.breeder_id,
      color: result.color ?? undefined,
      momId: result.mom_id ?? undefined,
      dadId: result.dad_id ?? undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }))
  } catch (error) {
    console.error('Get litters by breeder error:', error)
    return []
  }
}

// Update litter
export async function updateLitter(id: number, data: UpdateLitterData): Promise<Litter | null> {
  try {
    const updateData: any = {}
    
    if (data.litterCode !== undefined) updateData.litter_code = data.litterCode
    if (data.minExpectedSizeLbs !== undefined) updateData.min_expected_size_lbs = data.minExpectedSizeLbs
    if (data.maxExpectedSizeLbs !== undefined) updateData.max_expected_size_lbs = data.maxExpectedSizeLbs
    if (data.expectedBirthDate !== undefined) updateData.expected_birth_date = data.expectedBirthDate
    if (data.actualBirthDate !== undefined) updateData.actual_birth_date = data.actualBirthDate
    if (data.status !== undefined) updateData.status = data.status
    if (data.color !== undefined) updateData.color = data.color
    if (data.momId !== undefined) updateData.mom_id = data.momId
    if (data.dadId !== undefined) updateData.dad_id = data.dadId
    
    updateData.updated_at = new Date()

    const result = await db
      .updateTable('litters')
      .set(updateData)
      .where('id', '=', id)
      .returning([
        'id',
        'litter_code',
        'min_expected_size_lbs',
        'max_expected_size_lbs',
        'expected_birth_date',
        'actual_birth_date',
        'status',
        'breeder_id',
        'color',
        'mom_id',
        'dad_id',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      litterCode: result.litter_code,
      minExpectedSizeLbs: result.min_expected_size_lbs ?? undefined,
      maxExpectedSizeLbs: result.max_expected_size_lbs ?? undefined,
      expectedBirthDate: result.expected_birth_date ?? undefined,
      actualBirthDate: result.actual_birth_date ?? undefined,
      status: result.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
      breederId: result.breeder_id,
      color: result.color ?? undefined,
      momId: result.mom_id ?? undefined,
      dadId: result.dad_id ?? undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Update litter error:', error)
    return null
  }
}

// Delete litter
export async function deleteLitter(id: number, breederId: number): Promise<boolean> {
  try {
    const result = await db
      .deleteFrom('litters')
      .where('id', '=', id)
      .where('breeder_id', '=', breederId) // Ensure only the owner can delete
      .executeTakeFirst()

    return result.numDeletedRows > 0
  } catch (error) {
    console.error('Delete litter error:', error)
    return false
  }
}
