import { db } from '@/lib/database'
import { NextResponse } from 'next/server'

export interface SubscriptionLimits {
  max_litters: number | null
  max_puppies: number | null
  max_customers: number | null
  max_photos: number | null
  max_documents: number | null
}

export interface UsageCounts {
  litters: number
  puppies: number
  customers: number
  photos: number
  documents: number
}

export async function checkSubscriptionLimits(userId: number): Promise<{
  limits: SubscriptionLimits | null
  usage: UsageCounts
  hasActiveSubscription: boolean
}> {
  // Get user's current subscription
  const subscription = await db
    .selectFrom('subscriptions')
    .innerJoin('subscription_plans', 'subscriptions.plan_id', 'subscription_plans.id')
    .select([
      'subscription_plans.max_litters',
      'subscription_plans.max_puppies',
      'subscription_plans.max_customers',
      'subscription_plans.max_photos',
      'subscription_plans.max_documents',
    ])
    .where('subscriptions.user_id', '=', userId)
    .where('subscriptions.status', 'in', ['active', 'trialing'])
    .orderBy('subscriptions.created_at', 'desc')
    .executeTakeFirst()

  // Get current usage counts
  const [litterCount, puppyCount, customerCount, photoCount, documentCount] = await Promise.all([
    db.selectFrom('litters')
      .select(db.fn.countAll().as('count'))
      .where('breeder_id', '=', userId)
      .executeTakeFirst(),
    db.selectFrom('puppies')
      .select(db.fn.countAll().as('count'))
      .where('breeder_id', '=', userId)
      .executeTakeFirst(),
    db.selectFrom('customer_breeder_relationships')
      .select(db.fn.countAll().as('count'))
      .where('breeder_id', '=', userId)
      .executeTakeFirst(),
    db.selectFrom('photos')
      .select(db.fn.countAll().as('count'))
      .where('breeder_id', '=', userId)
      .executeTakeFirst(),
    db.selectFrom('documents')
      .select(db.fn.countAll().as('count'))
      .where('breeder_id', '=', userId)
      .executeTakeFirst(),
  ])

  const usage: UsageCounts = {
    litters: Number(litterCount?.count || 0),
    puppies: Number(puppyCount?.count || 0),
    customers: Number(customerCount?.count || 0),
    photos: Number(photoCount?.count || 0),
    documents: Number(documentCount?.count || 0),
  }

  return {
    limits: subscription || null,
    usage,
    hasActiveSubscription: !!subscription,
  }
}

export function checkLimitExceeded(
  resourceType: keyof UsageCounts,
  currentUsage: UsageCounts,
  limits: SubscriptionLimits | null
): boolean {
  if (!limits) {
    // No active subscription - allow limited usage (starter plan limits)
    const defaultLimits = {
      litters: 2,
      puppies: 20,
      customers: 50,
      photos: 30,
      documents: 30,
    }
    return currentUsage[resourceType] >= defaultLimits[resourceType]
  }

  const limit = limits[`max_${resourceType}` as keyof SubscriptionLimits]
  if (limit === null) {
    return false // Unlimited
  }

  return currentUsage[resourceType] >= limit
}

export function createLimitExceededResponse(
  resourceType: string,
  currentUsage: number,
  limit: number | null
) {
  const limitText = limit === null ? 'unlimited' : limit.toString()
  return NextResponse.json(
    {
      error: `${resourceType} limit reached`,
      message: `You have reached your ${resourceType} limit (${currentUsage}/${limitText}). Please upgrade your plan to continue.`,
      currentUsage,
      limit,
      upgradeUrl: '/breeder/settings#subscription'
    },
    { status: 403 }
  )
}

// Middleware function to check limits before creating resources
export async function withSubscriptionLimitCheck(
  userId: number,
  resourceType: keyof UsageCounts,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  const { limits, usage } = await checkSubscriptionLimits(userId)
  
  if (checkLimitExceeded(resourceType, usage, limits)) {
    const limit = limits ? limits[`max_${resourceType}` as keyof SubscriptionLimits] : 
                   resourceType === 'litters' ? 2 :
                   resourceType === 'puppies' ? 20 :
                   resourceType === 'customers' ? 50 :
                   resourceType === 'photos' ? 30 :
                   resourceType === 'documents' ? 30 : 0
    
    return createLimitExceededResponse(
      resourceType,
      usage[resourceType],
      limit
    )
  }

  return handler()
}
