import { db } from './database'

export interface CreateBreederData {
  userId: number
  businessName: string
  businessPhone?: string
  breederCode: string
  website?: string
  description?: string
  specialties?: string
  yearsExperience?: number
}

export interface UpdateBreederData {
  businessName?: string
  businessPhone?: string
  website?: string
  description?: string
  specialties?: string
  yearsExperience?: number
}

export interface Breeder {
  id: number
  userId: number
  businessName: string
  businessPhone?: string
  breederCode: string
  website?: string
  description?: string
  specialties?: string
  yearsExperience?: number
  createdAt: Date
  updatedAt: Date
}

export interface BreederWithUser {
  id: number
  userId: number
  businessName: string
  businessPhone?: string
  breederCode: string
  website?: string
  description?: string
  specialties?: string
  yearsExperience?: number
  createdAt: Date
  updatedAt: Date
  user: {
    id: number
    email: string
    firstName: string
    lastName: string
    phone?: string
    streetAddress?: string
    aptNumber?: string
    city?: string
    state?: string
    zipCode?: string
  }
}

// Generate a unique breeder code
export async function generateBreederCode(businessName: string, firstName: string, lastName: string): Promise<string> {
  const baseName = businessName || `${firstName} ${lastName}`
  let code = baseName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .substring(0, 20) // Limit length

  // Check if code exists and make it unique
  let counter = 1
  let finalCode = code
  
  while (true) {
    const existing = await db
      .selectFrom('breeders')
      .select(['id'])
      .where('breeder_code', '=', finalCode)
      .executeTakeFirst()
    
    if (!existing) {
      break
    }
    
    finalCode = `${code}-${counter}`
    counter++
  }
  
  return finalCode
}

// Create a new breeder
export async function createBreeder(data: CreateBreederData): Promise<Breeder> {
  try {
    const result = await db
      .insertInto('breeders')
      .values({
        user_id: data.userId,
        business_name: data.businessName,
        business_phone: data.businessPhone || null,
        breeder_code: data.breederCode,
        website: data.website || null,
        description: data.description || null,
        specialties: data.specialties || null,
        years_experience: data.yearsExperience || null,
      })
      .returning([
        'id',
        'user_id',
        'business_name',
        'business_phone',
        'breeder_code',
        'website',
        'description',
        'specialties',
        'years_experience',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return {
      id: result.id,
      userId: result.user_id,
      businessName: result.business_name,
      businessPhone: result.business_phone || undefined,
      breederCode: result.breeder_code,
      website: result.website || undefined,
      description: result.description || undefined,
      specialties: result.specialties || undefined,
      yearsExperience: result.years_experience || undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Create breeder error:', error)
    throw error
  }
}

// Get breeder by user ID
export async function getBreederByUserId(userId: number): Promise<Breeder | null> {
  try {
    const result = await db
      .selectFrom('breeders')
      .select([
        'id',
        'user_id',
        'business_name',
        'business_phone',
        'breeder_code',
        'website',
        'description',
        'specialties',
        'years_experience',
        'created_at',
        'updated_at'
      ])
      .where('user_id', '=', userId)
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      userId: result.user_id,
      businessName: result.business_name,
      businessPhone: result.business_phone || undefined,
      breederCode: result.breeder_code,
      website: result.website || undefined,
      description: result.description || undefined,
      specialties: result.specialties || undefined,
      yearsExperience: result.years_experience || undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Get breeder by user ID error:', error)
    return null
  }
}

// Get breeder by breeder code
export async function getBreederByCode(breederCode: string): Promise<BreederWithUser | null> {
  try {
    const result = await db
      .selectFrom('breeders')
      .innerJoin('users', 'breeders.user_id', 'users.id')
      .select([
        'breeders.id',
        'breeders.user_id',
        'breeders.business_name',
        'breeders.business_phone',
        'breeders.breeder_code',
        'breeders.website',
        'breeders.description',
        'breeders.specialties',
        'breeders.years_experience',
        'breeders.created_at',
        'breeders.updated_at',
        'users.email',
        'users.first_name',
        'users.last_name',
        'users.phone',
        'users.street_address',
        'users.apt_number',
        'users.city',
        'users.state',
        'users.zip_code'
      ])
      .where('breeders.breeder_code', '=', breederCode)
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      userId: result.user_id,
      businessName: result.business_name,
      businessPhone: result.business_phone || undefined,
      breederCode: result.breeder_code,
      website: result.website || undefined,
      description: result.description || undefined,
      specialties: result.specialties || undefined,
      yearsExperience: result.years_experience || undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
      user: {
        id: result.user_id,
        email: result.email,
        firstName: result.first_name,
        lastName: result.last_name,
        phone: result.phone || undefined,
        streetAddress: result.street_address || undefined,
        aptNumber: result.apt_number || undefined,
        city: result.city || undefined,
        state: result.state || undefined,
        zipCode: result.zip_code || undefined,
      }
    }
  } catch (error) {
    console.error('Get breeder by code error:', error)
    return null
  }
}

// Update breeder
export async function updateBreeder(breederId: number, data: UpdateBreederData): Promise<Breeder | null> {
  try {
    const updateData: any = {}
    
    if (data.businessName !== undefined) updateData.business_name = data.businessName
    if (data.businessPhone !== undefined) updateData.business_phone = data.businessPhone
    if (data.website !== undefined) updateData.website = data.website
    if (data.description !== undefined) updateData.description = data.description
    if (data.specialties !== undefined) updateData.specialties = data.specialties
    if (data.yearsExperience !== undefined) updateData.years_experience = data.yearsExperience
    
    updateData.updated_at = new Date()

    if (Object.keys(updateData).length === 1) { // Only updated_at
      return null
    }

    const result = await db
      .updateTable('breeders')
      .set(updateData)
      .where('id', '=', breederId)
      .returning([
        'id',
        'user_id',
        'business_name',
        'business_phone',
        'breeder_code',
        'website',
        'description',
        'specialties',
        'years_experience',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      userId: result.user_id,
      businessName: result.business_name,
      businessPhone: result.business_phone || undefined,
      breederCode: result.breeder_code,
      website: result.website || undefined,
      description: result.description || undefined,
      specialties: result.specialties || undefined,
      yearsExperience: result.years_experience || undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Update breeder error:', error)
    return null
  }
}
