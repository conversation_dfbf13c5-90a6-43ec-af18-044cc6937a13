import { <PERSON><PERSON><PERSON>, Postg<PERSON><PERSON><PERSON><PERSON><PERSON>, Generated } from 'kysely'
import { Pool } from 'pg'

// Database schema types
export interface CustomerBreederTable {
  id: Generated<number>
  customer_id: number
  breeder_id: number
  relationship_type: 'owner' | 'lead'
  status: 'active' | 'inactive'
  notes: string | null
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface BreederTable {
  id: Generated<number>
  user_id: number
  business_name: string
  business_phone: string | null
  breeder_code: string
  website: string | null
  description: string | null
  specialties: string | null
  years_experience: number | null
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface EventTable {
  id: Generated<number>
  breeder_id: number
  title: string
  description: string | null
  event_date: Date
  event_time: string | null
  event_type: 'appointment' | 'breeding' | 'health_check' | 'show' | 'training' | 'other'
  location: string | null
  all_day: boolean
  reminder_minutes: number | null
  status: 'scheduled' | 'completed' | 'cancelled'
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface Database {
  users: UserTable
  breeders: BreederTable
  litters: LitterTable
  puppies: PuppyTable
  photos: PhotoTable
  documents: DocumentTable
  customer_breeder_relationships: CustomerBreederTable
  events: EventTable
}

export interface UserTable {
  id: Generated<number>
  email: string
  password_hash: string
  user_type: 'customer' | 'breeder'
  first_name: string
  last_name: string
  phone: string | null
  street_address: string | null
  apt_number: string | null
  city: string | null
  state: string | null
  zip_code: string | null
  reset_token: string | null
  reset_token_expiry: Date | null
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface LitterTable {
  id: Generated<number>
  litter_code: string
  min_expected_size_lbs: number | null
  max_expected_size_lbs: number | null
  expected_birth_date: Date | null
  actual_birth_date: Date | null
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  breeder_id: number
  color: string | null
  mom_id: number | null
  dad_id: number | null
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface PuppyTable {
  id: Generated<number>
  name: string
  description: string | null
  birth_date: Date | null
  color: string | null
  price: number | null
  litter_id: number | null
  owner_id: number | null
  owner_type: 'breeder' | 'customer' | null
  created_at: Generated<Date>
  updated_at: Generated<Date>
}

export interface PhotoTable {
  id: Generated<number>
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  entity_type: 'litter' | 'puppy'
  entity_id: number
  created_at: Generated<Date>
}

export interface DocumentTable {
  id: Generated<number>
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  document_type: string | null
  entity_type: 'litter' | 'puppy'
  entity_id: number
  created_at: Generated<Date>
}

// Create database connection
const dialect = new PostgresDialect({
  pool: new Pool({
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
  })
})

export const db = new Kysely<Database>({
  dialect,
})

// Database initialization function
export async function initializeDatabase() {
  try {
    // Create users table if it doesn't exist
    await db.schema
      .createTable('users')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('email', 'varchar(255)', (col) => col.notNull().unique())
      .addColumn('password_hash', 'varchar(255)', (col) => col.notNull())
      .addColumn('user_type', 'varchar(20)', (col) => col.notNull())
      .addColumn('first_name', 'varchar(100)', (col) => col.notNull())
      .addColumn('last_name', 'varchar(100)', (col) => col.notNull())
      .addColumn('phone', 'varchar(20)')
      .addColumn('street_address', 'varchar(255)')
      .addColumn('apt_number', 'varchar(50)')
      .addColumn('city', 'varchar(100)')
      .addColumn('state', 'varchar(50)')
      .addColumn('zip_code', 'varchar(20)')
      .addColumn('business_name', 'varchar(255)')
      .addColumn('business_phone', 'varchar(20)')
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create puppies table first (referenced by litters)
    await db.schema
      .createTable('puppies')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('name', 'varchar(100)', (col) => col.notNull())
      .addColumn('description', 'text')
      .addColumn('birth_date', 'date')
      .addColumn('color', 'varchar(50)')
      .addColumn('litter_id', 'integer')
      .addColumn('owner_id', 'integer')
      .addColumn('owner_type', 'varchar(20)')
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create breeders table
    await db.schema
      .createTable('breeders')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('user_id', 'integer', (col) => col.notNull().unique())
      .addColumn('business_name', 'varchar(255)', (col) => col.notNull())
      .addColumn('business_phone', 'varchar(20)')
      .addColumn('breeder_code', 'varchar(50)', (col) => col.notNull().unique())
      .addColumn('website', 'varchar(255)')
      .addColumn('description', 'text')
      .addColumn('specialties', 'varchar(255)')
      .addColumn('years_experience', 'integer')
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create litters table
    await db.schema
      .createTable('litters')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('litter_code', 'varchar(50)', (col) => col.notNull().unique())
      .addColumn('min_expected_size_lbs', 'real')
      .addColumn('max_expected_size_lbs', 'real')
      .addColumn('expected_birth_date', 'date')
      .addColumn('actual_birth_date', 'date')
      .addColumn('status', 'varchar(20)', (col) => col.notNull().defaultTo('NOT_BORN'))
      .addColumn('breeder_id', 'integer', (col) => col.notNull())
      .addColumn('color', 'varchar(50)')
      .addColumn('mom_id', 'integer')
      .addColumn('dad_id', 'integer')
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create photos table
    await db.schema
      .createTable('photos')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('filename', 'varchar(255)', (col) => col.notNull())
      .addColumn('original_name', 'varchar(255)', (col) => col.notNull())
      .addColumn('file_path', 'varchar(500)', (col) => col.notNull())
      .addColumn('file_size', 'integer', (col) => col.notNull())
      .addColumn('mime_type', 'varchar(100)', (col) => col.notNull())
      .addColumn('entity_type', 'varchar(20)', (col) => col.notNull())
      .addColumn('entity_id', 'integer', (col) => col.notNull())
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create documents table
    await db.schema
      .createTable('documents')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('filename', 'varchar(255)', (col) => col.notNull())
      .addColumn('original_name', 'varchar(255)', (col) => col.notNull())
      .addColumn('file_path', 'varchar(500)', (col) => col.notNull())
      .addColumn('file_size', 'integer', (col) => col.notNull())
      .addColumn('mime_type', 'varchar(100)', (col) => col.notNull())
      .addColumn('document_type', 'varchar(100)')
      .addColumn('entity_type', 'varchar(20)', (col) => col.notNull())
      .addColumn('entity_id', 'integer', (col) => col.notNull())
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Create customer-breeder relationships table
    await db.schema
      .createTable('customer_breeder_relationships')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('customer_id', 'integer', (col) => col.notNull())
      .addColumn('breeder_id', 'integer', (col) => col.notNull())
      .addColumn('relationship_type', 'varchar(20)', (col) => col.notNull())
      .addColumn('status', 'varchar(20)', (col) => col.notNull().defaultTo('active'))
      .addColumn('notes', 'text')
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Add unique constraint for customer-breeder relationship
    try {
      await db.schema
        .createIndex('customer_breeder_unique')
        .on('customer_breeder_relationships')
        .columns(['customer_id', 'breeder_id'])
        .unique()
        .execute()
    } catch (error) {
      // Index might already exist, ignore
    }

    // Create events table
    await db.schema
      .createTable('events')
      .ifNotExists()
      .addColumn('id', 'serial', (col) => col.primaryKey())
      .addColumn('breeder_id', 'integer', (col) => col.notNull())
      .addColumn('title', 'varchar(255)', (col) => col.notNull())
      .addColumn('description', 'text')
      .addColumn('event_date', 'date', (col) => col.notNull())
      .addColumn('event_time', 'varchar(10)')
      .addColumn('event_type', 'varchar(50)', (col) => col.notNull().defaultTo('other'))
      .addColumn('location', 'varchar(255)')
      .addColumn('all_day', 'boolean', (col) => col.notNull().defaultTo(false))
      .addColumn('reminder_minutes', 'integer')
      .addColumn('status', 'varchar(20)', (col) => col.notNull().defaultTo('scheduled'))
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .addColumn('updated_at', 'timestamp', (col) => col.defaultTo('now()').notNull())
      .execute()

    // Add foreign key constraints (skip if they already exist)
    // Note: We'll skip foreign key constraints for now to avoid circular dependency issues
    // and constraint already exists errors. The application will work fine without them
    // as we handle referential integrity in the application layer.

    // Add new address columns if they don't exist
    try {
      // First, make physical_address nullable if it exists
      await db.schema
        .alterTable('users')
        .alterColumn('physical_address', (col) => col.dropNotNull())
        .execute()
    } catch (error) {
      // Column might not exist or already nullable, ignore
    }

    try {
      // Check if the new columns exist by trying to add them
      await db.schema
        .alterTable('users')
        .addColumn('phone', 'varchar(20)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('street_address', 'varchar(255)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('apt_number', 'varchar(50)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('city', 'varchar(100)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('state', 'varchar(50)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('zip_code', 'varchar(20)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('reset_token', 'varchar(255)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('reset_token_expiry', 'timestamp')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('users')
        .addColumn('breeder_code', 'varchar(50)')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await db.schema
        .alterTable('puppies')
        .addColumn('price', 'integer')
        .execute()
    } catch (error) {
      // Column already exists, ignore
    }

    console.log('Database tables created successfully')

    console.log('Database initialized successfully')
  } catch (error) {
    console.error('Error initializing database:', error)
    throw error
  }
}
