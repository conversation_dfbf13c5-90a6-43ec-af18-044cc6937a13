// Email service utility
// This is a placeholder for email functionality
// In production, integrate with services like SendGrid, Nodemailer, AWS SES, etc.

import nodemailer from 'nodemailer'

interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
}

export async function sendEmail(data: EmailData): Promise<boolean> {
  // For development, log the email to console
  console.log('=== EMAIL NOTIFICATION ===')
  console.log(`To: ${data.to}`)
  console.log(`Subject: ${data.subject}`)
  console.log(`HTML Content:`)
  console.log(data.html)
  if (data.text) {
    console.log(`Text Content:`)
    console.log(data.text)
  }
  console.log('==========================')

  // TODO: Replace with actual email service
  // Example implementations:

  /* 
  // SendGrid example:
  const sgMail = require('@sendgrid/mail')
  sgMail.setApiKey(process.env.SENDGRID_API_KEY)
  
  try {
    await sgMail.send({
      to: data.to,
      from: process.env.FROM_EMAIL,
      subject: data.subject,
      html: data.html,
      text: data.text
    })
    return true
  } catch (error) {
    console.error('SendGrid error:', error)
    return false
  }
  */



  // Nodemailer example:
  
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.SMTP_USER,
      pass: "cpyl bvtf nilj pzsv"
    }
  })
  
  try {
    await transporter.sendMail({
      from: process.env.FROM_EMAIL,
      to: data.to,
      subject: data.subject,
      html: data.html,
      text: data.text
    })
    return true
  } catch (error) {
    console.error('Nodemailer error:', error)
    return false
  }

}

export function generatePasswordResetEmail(
  userName: string,
  resetUrl: string
): { subject: string; html: string; text: string } {
  const subject = 'Reset Your PawLedger Password'
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #3B82F6; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .button { 
          display: inline-block; 
          background-color: #3B82F6; 
          color: white; 
          padding: 12px 24px; 
          text-decoration: none; 
          border-radius: 6px; 
          margin: 20px 0;
        }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>PawLedger</h1>
        </div>
        <div class="content">
          <h2>Password Reset Request</h2>
          <p>Hello ${userName},</p>
          <p>You requested a password reset for your PawLedger account. Click the button below to reset your password:</p>
          <p style="text-align: center;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </p>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
            ${resetUrl}
          </p>
          <p><strong>This link will expire in 1 hour.</strong></p>
          <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
          <p>For security reasons, this link can only be used once.</p>
        </div>
        <div class="footer">
          <p>Best regards,<br>The PawLedger Team</p>
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    PawLedger - Password Reset Request
    
    Hello ${userName},
    
    You requested a password reset for your PawLedger account.
    
    Click the link below to reset your password:
    ${resetUrl}
    
    This link will expire in 1 hour.
    
    If you didn't request this password reset, please ignore this email.
    
    Best regards,
    The PawLedger Team
  `
  
  return { subject, html, text }
}
