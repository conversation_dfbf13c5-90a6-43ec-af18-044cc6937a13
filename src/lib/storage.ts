import { Storage } from '@google-cloud/storage'
import { v4 as uuidv4 } from 'uuid'

// Initialize Google Cloud Storage
const storage = new Storage({
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  // For local development, use keyFilename
  ...(process.env.GOOGLE_CLOUD_KEY_FILE && { keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE }),
  // For production (Vercel), use base64 encoded credentials
  ...(process.env.GOOGLE_CLOUD_KEY_FILE_BASE64 && {
    credentials: JSON.parse(
      Buffer.from(process.env.GOOGLE_CLOUD_KEY_FILE_BASE64, 'base64').toString('utf-8')
    )
  }),
})

const bucketName = process.env.GOOGLE_CLOUD_STORAGE_BUCKET || 'pawledger-images'
const bucket = storage.bucket(bucketName)

export interface UploadResult {
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
}

export async function uploadImage(
  file: Buffer,
  originalName: string,
  mimeType: string,
  entityType: 'litter' | 'puppy',
  entityId: number
): Promise<UploadResult> {
  try {
    // Generate unique filename
    const fileExtension = originalName.split('.').pop()
    const filename = `${entityType}s/${entityId}/${uuidv4()}.${fileExtension}`
    
    // Create file reference in bucket
    const fileRef = bucket.file(filename)
    
    // Upload file
    await fileRef.save(file, {
      metadata: {
        contentType: mimeType,
        metadata: {
          originalName,
          entityType,
          entityId: entityId.toString(),
          uploadedAt: new Date().toISOString(),
        },
      },
    })

    // Make file publicly readable
    await fileRef.makePublic()

    // Get public URL
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${filename}`

    return {
      filename,
      originalName,
      url: publicUrl,
      size: file.length,
      mimeType,
    }
  } catch (error) {
    console.error('Error uploading to Google Cloud Storage:', error)
    throw new Error('Failed to upload image')
  }
}

export async function deleteImage(filename: string): Promise<boolean> {
  try {
    const fileRef = bucket.file(filename)
    await fileRef.delete()
    return true
  } catch (error) {
    console.error('Error deleting from Google Cloud Storage:', error)
    return false
  }
}

export async function listImages(entityType: 'litter' | 'puppy', entityId: number): Promise<string[]> {
  try {
    const prefix = `${entityType}s/${entityId}/`
    const [files] = await bucket.getFiles({ prefix })
    
    return files.map(file => `https://storage.googleapis.com/${bucketName}/${file.name}`)
  } catch (error) {
    console.error('Error listing images from Google Cloud Storage:', error)
    return []
  }
}

// Validate image file
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024 // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Only JPEG, PNG, and WebP images are allowed'
    }
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image size must be less than 10MB'
    }
  }

  return { valid: true }
}

// Convert File to Buffer (for use in API routes)
export async function fileToBuffer(file: File): Promise<Buffer> {
  const arrayBuffer = await file.arrayBuffer()
  return Buffer.from(arrayBuffer)
}
