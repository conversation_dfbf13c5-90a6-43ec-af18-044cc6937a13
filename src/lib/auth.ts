import bcrypt from 'bcryptjs'
import { db } from './database'
import { generateBreederCode } from './breeder-public'

export interface CreateUserData {
  email: string
  password: string
  userType: 'customer' | 'breeder'
  firstName: string
  lastName: string
  phone?: string
  streetAddress?: string
  aptNumber?: string
  city?: string
  state?: string
  zipCode?: string
  businessName?: string
  businessPhone?: string
}

export interface LoginData {
  email: string
  password: string
}

export interface User {
  id: number
  email: string
  userType: 'customer' | 'breeder'
  firstName: string
  lastName: string
  phone?: string
  streetAddress?: string
  aptNumber?: string
  city?: string
  state?: string
  zipCode?: string
  businessName?: string
  businessPhone?: string
  breederCode?: string
  createdAt?: Date
  updatedAt?: Date
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// Verify password
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

// Create new user
export async function createUser(userData: CreateUserData): Promise<User> {
  const hashedPassword = await hashPassword(userData.password)

  // Generate breeder code for breeders
  let breederCode = null
  if (userData.userType === 'breeder') {
    breederCode = await generateBreederCode(
      userData.businessName || '',
      userData.firstName,
      userData.lastName
    )
  }

  try {
    const result = await db
      .insertInto('users')
      .values({
        email: userData.email,
        password_hash: hashedPassword,
        user_type: userData.userType,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone || null,
        street_address: userData.streetAddress || null,
        apt_number: userData.aptNumber || null,
        city: userData.city || null,
        state: userData.state || null,
        zip_code: userData.zipCode || null,
        business_name: userData.businessName || null,
        business_phone: userData.businessPhone || null,
        breeder_code: breederCode,
      })
      .returning([
        'id',
        'email',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zip_code',
        'business_name',
        'business_phone',
        'breeder_code'
      ])
      .executeTakeFirstOrThrow()

    return {
      id: result.id,
      email: result.email,
      userType: result.user_type as 'customer' | 'breeder',
      firstName: result.first_name,
      lastName: result.last_name,
      phone: result.phone ?? undefined,
      streetAddress: result.street_address ?? undefined,
      aptNumber: result.apt_number ?? undefined,
      city: result.city ?? undefined,
      state: result.state ?? undefined,
      zipCode: result.zip_code ?? undefined,
      businessName: result.business_name ?? undefined,
      businessPhone: result.business_phone ?? undefined,
      breederCode: result.breeder_code ?? undefined,
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes('unique constraint')) {
      throw new Error('Email already exists')
    }
    throw error
  }
}

// Authenticate user
export async function authenticateUser(loginData: LoginData): Promise<User | null> {
  try {
    const user = await db
      .selectFrom('users')
      .select([
        'id',
        'email',
        'password_hash',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zip_code',
        'business_name',
        'business_phone',
        'breeder_code'
      ])
      .where('email', '=', loginData.email)
      .executeTakeFirst()

    if (!user) {
      return null
    }

    const isValidPassword = await verifyPassword(loginData.password, user.password_hash)
    if (!isValidPassword) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      userType: user.user_type as 'customer' | 'breeder',
      firstName: user.first_name,
      lastName: user.last_name,
      phone: user.phone ?? undefined,
      streetAddress: user.street_address ?? undefined,
      aptNumber: user.apt_number ?? undefined,
      city: user.city ?? undefined,
      state: user.state ?? undefined,
      zipCode: user.zip_code ?? undefined,
      businessName: user.business_name ?? undefined,
      businessPhone: user.business_phone ?? undefined,
      breederCode: user.breeder_code ?? undefined,
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

// Get user by ID
export async function getUserById(id: number): Promise<User | null> {
  try {
    const user = await db
      .selectFrom('users')
      .select([
        'id',
        'email',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zip_code',
        'business_name',
        'business_phone',
        'breeder_code'
      ])
      .where('id', '=', id)
      .executeTakeFirst()

    if (!user) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      userType: user.user_type as 'customer' | 'breeder',
      firstName: user.first_name,
      lastName: user.last_name,
      phone: user.phone ?? undefined,
      streetAddress: user.street_address ?? undefined,
      aptNumber: user.apt_number ?? undefined,
      city: user.city ?? undefined,
      state: user.state ?? undefined,
      zipCode: user.zip_code ?? undefined,
      businessName: user.business_name ?? undefined,
      businessPhone: user.business_phone ?? undefined,
      breederCode: user.breeder_code ?? undefined,
    }
  } catch (error) {
    console.error('Get user error:', error)
    return null
  }
}
