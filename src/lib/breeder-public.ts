import { db } from './database'

export interface PublicBreederProfile {
  id: number
  breederCode: string
  businessName: string
  firstName: string
  lastName: string
  phone?: string
  businessPhone?: string
  streetAddress?: string
  city?: string
  state?: string
  zipCode?: string
  email: string
  website?: string
  description?: string
  specialties?: string
  yearsExperience?: number
  logoUrl?: string
}

export interface PublicLitter {
  id: number
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color?: string
  momName?: string
  dadName?: string
  puppyCount: number
  availablePuppyCount: number
  createdAt: Date
}

export interface PublicPuppy {
  id: number
  name: string
  description?: string
  birthDate?: Date
  color?: string
  price?: number
  isAvailable: boolean
  litterCode?: string
  photos: PublicPhoto[]
}

export interface PublicPhoto {
  id: number
  filename: string
  url: string
}

// Get breeder profile by breeder code
export async function getBreederByCode(breederCode: string): Promise<PublicBreederProfile | null> {
  try {
    const result = await db
      .selectFrom('breeders')
      .innerJoin('users', 'breeders.user_id', 'users.id')
      .select([
        'breeders.id',
        'breeders.breeder_code',
        'breeders.business_name',
        'breeders.business_phone',
        'breeders.website',
        'breeders.description',
        'breeders.specialties',
        'breeders.years_experience',
        'breeders.logo_url',
        'users.first_name',
        'users.last_name',
        'users.phone',
        'users.street_address',
        'users.city',
        'users.state',
        'users.zip_code',
        'users.email'
      ])
      .where('breeders.breeder_code', '=', breederCode)
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      breederCode: result.breeder_code,
      businessName: result.business_name,
      firstName: result.first_name,
      lastName: result.last_name,
      phone: result.phone || undefined,
      businessPhone: result.business_phone || undefined,
      streetAddress: result.street_address || undefined,
      city: result.city || undefined,
      state: result.state || undefined,
      zipCode: result.zip_code || undefined,
      email: result.email,
      website: result.website || undefined,
      description: result.description || undefined,
      specialties: result.specialties || undefined,
      yearsExperience: result.years_experience || undefined,
      logoUrl: result.logo_url || undefined,
    }
  } catch (error) {
    console.error('Get breeder by code error:', error)
    return null
  }
}

// Get public litters for a breeder
export async function getPublicLitters(breederId: number): Promise<PublicLitter[]> {
  try {
    const results = await db
      .selectFrom('litters')
      .leftJoin('puppies as mom', 'litters.mom_id', 'mom.id')
      .leftJoin('puppies as dad', 'litters.dad_id', 'dad.id')
      .select([
        'litters.id',
        'litters.litter_code',
        'litters.min_expected_size_lbs',
        'litters.max_expected_size_lbs',
        'litters.expected_birth_date',
        'litters.actual_birth_date',
        'litters.status',
        'litters.color',
        'litters.created_at',
        'mom.name as mom_name',
        'dad.name as dad_name'
      ])
      .where('litters.breeder_id', '=', breederId)
      .orderBy('litters.created_at', 'desc')
      .execute()

    // Get puppy counts for each litter
    const littersWithCounts = await Promise.all(
      results.map(async (litter) => {
        const puppyCountResult = await db
          .selectFrom('puppies')
          .select([
            db.fn.count('id').as('total_count'),
            db.fn.count('id').filterWhere('owner_type', '=', 'breeder').as('available_count')
          ])
          .where('litter_id', '=', litter.id)
          .executeTakeFirst()

        return {
          id: litter.id,
          litterCode: litter.litter_code,
          minExpectedSizeLbs: litter.min_expected_size_lbs || undefined,
          maxExpectedSizeLbs: litter.max_expected_size_lbs || undefined,
          expectedBirthDate: litter.expected_birth_date || undefined,
          actualBirthDate: litter.actual_birth_date || undefined,
          status: litter.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
          color: litter.color || undefined,
          momName: litter.mom_name || undefined,
          dadName: litter.dad_name || undefined,
          puppyCount: Number(puppyCountResult?.total_count || 0),
          availablePuppyCount: Number(puppyCountResult?.available_count || 0),
          createdAt: litter.created_at
        }
      })
    )

    return littersWithCounts
  } catch (error) {
    console.error('Get public litters error:', error)
    return []
  }
}

// Get available puppies for a breeder
export async function getAvailablePuppies(breederId: number): Promise<PublicPuppy[]> {
  try {
    const results = await db
      .selectFrom('puppies')
      .leftJoin('litters', 'puppies.litter_id', 'litters.id')
      .select([
        'puppies.id',
        'puppies.name',
        'puppies.description',
        'puppies.birth_date',
        'puppies.color',
        'puppies.price',
        'puppies.owner_type',
        'litters.litter_code'
      ])
      .where('litters.breeder_id', '=', breederId)
      .where('puppies.owner_type', '=', 'breeder') // Only available puppies
      .orderBy('puppies.birth_date', 'desc')
      .execute()

    // Get photos for each puppy
    const puppiesWithPhotos = await Promise.all(
      results.map(async (puppy) => {
        const photos = await db
          .selectFrom('photos')
          .select(['id', 'filename', 'file_path'])
          .where('entity_type', '=', 'puppy')
          .where('entity_id', '=', puppy.id)
          .execute()

        return {
          id: puppy.id,
          name: puppy.name,
          description: puppy.description || undefined,
          birthDate: puppy.birth_date || undefined,
          color: puppy.color || undefined,
          price: puppy.price ? puppy.price / 100 : undefined, // Convert from cents
          isAvailable: puppy.owner_type === 'breeder',
          litterCode: puppy.litter_code || undefined,
          photos: photos.map(photo => ({
            id: photo.id,
            filename: photo.filename,
            url: photo.file_path
          }))
        }
      })
    )

    return puppiesWithPhotos
  } catch (error) {
    console.error('Get available puppies error:', error)
    return []
  }
}

// Get puppies for a specific litter
export async function getLitterPuppies(litterId: number): Promise<PublicPuppy[]> {
  try {
    const results = await db
      .selectFrom('puppies')
      .leftJoin('litters', 'puppies.litter_id', 'litters.id')
      .select([
        'puppies.id',
        'puppies.name',
        'puppies.description',
        'puppies.birth_date',
        'puppies.color',
        'puppies.price',
        'puppies.owner_type',
        'litters.litter_code'
      ])
      .where('puppies.litter_id', '=', litterId)
      .orderBy('puppies.name', 'asc')
      .execute()

    // Get photos for each puppy
    const puppiesWithPhotos = await Promise.all(
      results.map(async (puppy) => {
        const photos = await db
          .selectFrom('photos')
          .select(['id', 'filename', 'file_path'])
          .where('entity_type', '=', 'puppy')
          .where('entity_id', '=', puppy.id)
          .execute()

        return {
          id: puppy.id,
          name: puppy.name,
          description: puppy.description || undefined,
          birthDate: puppy.birth_date || undefined,
          color: puppy.color || undefined,
          price: puppy.price ? puppy.price / 100 : undefined, // Convert from cents
          isAvailable: puppy.owner_type === 'breeder',
          litterCode: puppy.litter_code || undefined,
          photos: photos.map(photo => ({
            id: photo.id,
            filename: photo.filename,
            url: photo.file_path
          }))
        }
      })
    )

    return puppiesWithPhotos
  } catch (error) {
    console.error('Get litter puppies error:', error)
    return []
  }
}


