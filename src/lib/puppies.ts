import { db } from './database'

export interface CreatePuppyData {
  name: string
  description?: string
  birthDate?: Date
  color?: string
  price?: number
  litterId?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
}

export interface UpdatePuppyData {
  name?: string
  description?: string
  birthDate?: Date
  color?: string
  price?: number
  litterId?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
}

export interface Puppy {
  id: number
  name: string
  description?: string
  birthDate?: Date
  color?: string
  price?: number
  litterId?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
  createdAt: Date
  updatedAt: Date
}

// Create a new puppy
export async function createPuppy(data: CreatePuppyData): Promise<Puppy> {
  try {
    const result = await db
      .insertInto('puppies')
      .values({
        name: data.name,
        description: data.description || null,
        birth_date: data.birthDate || null,
        color: data.color || null,
        price: data.price ? Math.round(data.price * 100) : null, // Store as cents
        litter_id: data.litterId || null,
        owner_id: data.ownerId || null,
        owner_type: data.ownerType || null,
      })
      .returning([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return {
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      price: result.price ? result.price / 100 : undefined, // Convert from cents
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    throw error
  }
}

// Get puppy by ID
export async function getPuppyById(id: number): Promise<Puppy | null> {
  try {
    const result = await db
      .selectFrom('puppies')
      .select([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .where('id', '=', id)
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      price: result.price ? result.price / 100 : undefined, // Convert from cents
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Get puppy error:', error)
    return null
  }
}

// Get puppies by litter ID
export async function getPuppiesByLitterId(litterId: number): Promise<Puppy[]> {
  try {
    const results = await db
      .selectFrom('puppies')
      .select([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .where('litter_id', '=', litterId)
      .orderBy('name', 'asc')
      .execute()

    return results.map(result => ({
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      price: result.price ? result.price / 100 : undefined, // Convert from cents
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }))
  } catch (error) {
    console.error('Get puppies by litter error:', error)
    return []
  }
}

// Get puppies by owner
export async function getPuppiesByOwner(ownerId: number, ownerType: 'breeder' | 'customer'): Promise<Puppy[]> {
  try {
    const results = await db
      .selectFrom('puppies')
      .select([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .where('owner_id', '=', ownerId)
      .where('owner_type', '=', ownerType)
      .orderBy('name', 'asc')
      .execute()

    return results.map(result => ({
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      price: result.price ? result.price / 100 : undefined, // Convert from cents
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }))
  } catch (error) {
    console.error('Get puppies by owner error:', error)
    return []
  }
}

// Update puppy
export async function updatePuppy(id: number, data: UpdatePuppyData): Promise<Puppy | null> {
  try {
    const updateData: Record<string, unknown> = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.birthDate !== undefined) updateData.birth_date = data.birthDate
    if (data.color !== undefined) updateData.color = data.color
    if (data.price !== undefined) updateData.price = data.price ? Math.round(data.price * 100) : null // Store as cents
    if (data.litterId !== undefined) updateData.litter_id = data.litterId
    if (data.ownerId !== undefined) updateData.owner_id = data.ownerId
    if (data.ownerType !== undefined) updateData.owner_type = data.ownerType
    
    updateData.updated_at = new Date()

    const result = await db
      .updateTable('puppies')
      .set(updateData)
      .where('id', '=', id)
      .returning([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirst()

    if (!result) {
      return null
    }

    return {
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      price: result.price ? result.price / 100 : undefined, // Convert from cents
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }
  } catch (error) {
    console.error('Update puppy error:', error)
    return null
  }
}

// Delete puppy
export async function deletePuppy(id: number): Promise<boolean> {
  try {
    const result = await db
      .deleteFrom('puppies')
      .where('id', '=', id)
      .executeTakeFirst()

    return result.numDeletedRows > 0
  } catch (error) {
    console.error('Delete puppy error:', error)
    return false
  }
}

// Get available breeding dogs (puppies that can be parents)
export async function getBreedingDogs(breederId: number): Promise<Puppy[]> {
  try {
    const results = await db
      .selectFrom('puppies')
      .select([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .where('owner_id', '=', breederId)
      .where('owner_type', '=', 'breeder')
      .orderBy('name', 'asc')
      .execute()

    return results.map(result => ({
      id: result.id,
      name: result.name,
      description: result.description ?? undefined,
      birthDate: result.birth_date ?? undefined,
      color: result.color ?? undefined,
      litterId: result.litter_id ?? undefined,
      ownerId: result.owner_id ?? undefined,
      ownerType: result.owner_type as 'breeder' | 'customer' | undefined,
      createdAt: result.created_at,
      updatedAt: result.updated_at,
    }))
  } catch (error) {
    console.error('Get breeding dogs error:', error)
    return []
  }
}
