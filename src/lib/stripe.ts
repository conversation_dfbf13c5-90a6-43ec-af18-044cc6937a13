import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
})

// Subscription plan configurations matching your pricing
export const SUBSCRIPTION_PLANS = {
  starter: {
    name: 'Starter',
    priceMonthly: 1900, // $19.00 in cents
    priceYearly: 19000, // $190.00 in cents (2 months free)
    maxLitters: 2,
    maxPuppies: 20,
    maxCustomers: 50,
    maxPhotos: 30,
    maxDocuments: 30,
    features: [
      'Up to 2 litters',
      'Up to 20 puppies',
      'Customer management (50 customers and leads)',
      'Photo storage (30 photos)',
      'Document storage (30 documents)',
      'Basic public website',
      'Calendar & event tracking'
    ]
  },
  professional: {
    name: 'Professional',
    priceMonthly: 4900, // $49.00 in cents
    priceYearly: 49000, // $490.00 in cents (2 months free)
    maxLitters: 10,
    maxPuppies: 100,
    maxCustomers: 150,
    maxPhotos: 200,
    maxDocuments: 200,
    features: [
      'Everything in Starter, plus:',
      'Up to 10 litters',
      'Up to 100 puppies',
      'Customer management (150 customers and leads)',
      'Photo storage (200 photos)',
      'Document storage (200 documents)',
      'Custom branding & logo',
      'Advanced public website',
      'Custom domain support',
      'Detailed analytics & reporting',
      'Waiting list management',
      'Customer relationship tracking'
    ]
  },
  premium: {
    name: 'Premium',
    priceMonthly: 9900, // $99.00 in cents
    priceYearly: 99000, // $990.00 in cents (2 months free)
    maxLitters: null, // unlimited
    maxPuppies: null, // unlimited
    maxCustomers: null, // unlimited
    maxPhotos: 1000,
    maxDocuments: 1000,
    features: [
      'Everything in Professional, plus:',
      'Unlimited litters',
      'Unlimited puppies',
      'Unlimited customers',
      'Photo storage (1000 photos)',
      'Document storage (1000 documents)',
      'Automated email notifications',
      'Pedigree tracking'
    ]
  }
} as const

export type PlanType = keyof typeof SUBSCRIPTION_PLANS

// Helper function to create Stripe products and prices
export async function createStripeProducts() {
  const products = []
  
  for (const [planKey, plan] of Object.entries(SUBSCRIPTION_PLANS)) {
    // Create product
    const product = await stripe.products.create({
      name: `PawLedger ${plan.name}`,
      description: `${plan.name} plan for dog breeders`,
      metadata: {
        planType: planKey,
      }
    })

    // Create monthly price
    const monthlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.priceMonthly,
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      metadata: {
        planType: planKey,
        interval: 'monthly',
      }
    })

    // Create yearly price
    const yearlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.priceYearly,
      currency: 'usd',
      recurring: {
        interval: 'year',
      },
      metadata: {
        planType: planKey,
        interval: 'yearly',
      }
    })

    products.push({
      planType: planKey,
      product,
      monthlyPrice,
      yearlyPrice,
    })
  }

  return products
}

// Helper function to get or create Stripe customer
export async function getOrCreateStripeCustomer(userId: number, email: string, name?: string) {
  // First check if customer already exists in our database
  const existingSubscription = await db
    .selectFrom('subscriptions')
    .select('stripe_customer_id')
    .where('user_id', '=', userId)
    .executeTakeFirst()

  if (existingSubscription?.stripe_customer_id) {
    return existingSubscription.stripe_customer_id
  }

  // Create new Stripe customer
  const customer = await stripe.customers.create({
    email,
    name,
    metadata: {
      userId: userId.toString(),
    }
  })

  return customer.id
}

// Helper function to create checkout session
export async function createCheckoutSession({
  userId,
  email,
  planType,
  interval,
  successUrl,
  cancelUrl,
}: {
  userId: number
  email: string
  planType: PlanType
  interval: 'monthly' | 'yearly'
  successUrl: string
  cancelUrl: string
}) {
  const customerId = await getOrCreateStripeCustomer(userId, email)
  
  // Get the appropriate price ID (you'll need to store these after creating products)
  const priceId = await getPriceId(planType, interval)
  
  const session = await stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: successUrl,
    cancel_url: cancelUrl,
    trial_period_days: 14, // 14-day free trial
    metadata: {
      userId: userId.toString(),
      planType,
      interval,
    }
  })

  return session
}

// Helper function to get price ID from database
async function getPriceId(planType: PlanType, interval: 'monthly' | 'yearly'): Promise<string> {
  // This would query your subscription_plans table to get the stripe_price_id
  // For now, return a placeholder - you'll need to implement this based on your database
  throw new Error('getPriceId not implemented - need to query subscription_plans table')
}

// Helper function to handle subscription status changes
export async function updateSubscriptionStatus(
  stripeSubscriptionId: string,
  status: string,
  currentPeriodStart: number,
  currentPeriodEnd: number,
  trialEnd?: number | null,
  canceledAt?: number | null
) {
  const subscription = await db
    .updateTable('subscriptions')
    .set({
      status: status as any, // You may need to map Stripe statuses to your enum
      current_period_start: new Date(currentPeriodStart * 1000),
      current_period_end: new Date(currentPeriodEnd * 1000),
      trial_end: trialEnd ? new Date(trialEnd * 1000) : null,
      canceled_at: canceledAt ? new Date(canceledAt * 1000) : null,
      updated_at: new Date(),
    })
    .where('stripe_subscription_id', '=', stripeSubscriptionId)
    .execute()

  return subscription
}

// Helper function to check if user has reached plan limits
export async function checkPlanLimits(userId: number) {
  // Get user's current subscription
  const subscription = await db
    .selectFrom('subscriptions')
    .innerJoin('subscription_plans', 'subscriptions.plan_id', 'subscription_plans.id')
    .select([
      'subscription_plans.max_litters',
      'subscription_plans.max_puppies',
      'subscription_plans.max_customers',
      'subscription_plans.max_photos',
      'subscription_plans.max_documents',
    ])
    .where('subscriptions.user_id', '=', userId)
    .where('subscriptions.status', '=', 'active')
    .executeTakeFirst()

  if (!subscription) {
    return null // No active subscription
  }

  // Get current usage counts
  const [litterCount, puppyCount, customerCount, photoCount, documentCount] = await Promise.all([
    db.selectFrom('litters').select(db.fn.count('id').as('count')).where('breeder_id', '=', userId).executeTakeFirst(),
    db.selectFrom('puppies').select(db.fn.count('id').as('count')).where('breeder_id', '=', userId).executeTakeFirst(),
    db.selectFrom('customer_breeder_relationships').select(db.fn.count('id').as('count')).where('breeder_id', '=', userId).executeTakeFirst(),
    db.selectFrom('photos').select(db.fn.count('id').as('count')).where('breeder_id', '=', userId).executeTakeFirst(),
    db.selectFrom('documents').select(db.fn.count('id').as('count')).where('breeder_id', '=', userId).executeTakeFirst(),
  ])

  return {
    limits: subscription,
    usage: {
      litters: Number(litterCount?.count || 0),
      puppies: Number(puppyCount?.count || 0),
      customers: Number(customerCount?.count || 0),
      photos: Number(photoCount?.count || 0),
      documents: Number(documentCount?.count || 0),
    }
  }
}
