import { db } from '@/lib/database'
import { SUBSCRIPTION_PLANS } from '@/lib/stripe'

async function seedSubscriptionPlans() {
  console.log('Seeding subscription plans...')

  try {
    // Clear existing plans
    await db.deleteFrom('subscription_plans').execute()

    // Insert new plans
    for (const [planKey, plan] of Object.entries(SUBSCRIPTION_PLANS)) {
      await db
        .insertInto('subscription_plans')
        .values({
          name: planKey,
          stripe_price_id: `price_${planKey}_monthly`, // Placeholder - replace with actual Stripe price IDs
          stripe_product_id: `prod_${planKey}`, // Placeholder - replace with actual Stripe product IDs
          price_monthly: plan.priceMonthly,
          price_yearly: plan.priceYearly,
          max_litters: plan.maxLitters,
          max_puppies: plan.maxPuppies,
          max_customers: plan.maxCustomers,
          max_photos: plan.maxPhotos,
          max_documents: plan.maxDocuments,
          features: JSON.stringify(plan.features),
        })
        .execute()

      console.log(`✅ Seeded ${plan.name} plan`)
    }

    console.log('✅ Subscription plans seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding subscription plans:', error)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  seedSubscriptionPlans()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export { seedSubscriptionPlans }
