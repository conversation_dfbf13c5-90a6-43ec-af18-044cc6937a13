'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/Button'

interface Event {
  id: number
  title: string
  description: string
  eventDate: Date
  eventTime: string
  eventType: string
  location: string
  allDay: boolean
  reminderMinutes: number | null
  status: string
  createdAt: Date
  updatedAt: Date
}

interface EventDetailModalProps {
  event: Event
  onClose: () => void
  onEdit: () => void
  onDelete: () => void
}

const EVENT_TYPE_LABELS = {
  'appointment': 'Appointment',
  'breeding': 'Breeding',
  'health_check': 'Health Check',
  'show': 'Dog Show',
  'training': 'Training',
  'other': 'Other',
}

const EVENT_TYPE_COLORS = {
  'appointment': 'bg-blue-100 text-blue-800',
  'breeding': 'bg-pink-100 text-pink-800',
  'health_check': 'bg-green-100 text-green-800',
  'show': 'bg-purple-100 text-purple-800',
  'training': 'bg-yellow-100 text-yellow-800',
  'other': 'bg-gray-100 text-gray-800',
}

export function EventDetailModal({ event, onClose, onEdit, onDelete }: EventDetailModalProps) {
  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [onClose])

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const getEventTypeBadge = (type: string) => {
    const colorClass = EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] || EVENT_TYPE_COLORS.other
    const label = EVENT_TYPE_LABELS[type as keyof typeof EVENT_TYPE_LABELS] || 'Other'

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {label}
      </span>
    )
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (time: string, allDay: boolean) => {
    if (allDay) {
      return 'All day'
    }
    if (!time) {
      return 'No time specified'
    }
    
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getReminderText = (minutes: number | null) => {
    if (!minutes) return 'No reminder'
    
    if (minutes < 60) {
      return `${minutes} minutes before`
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60)
      return `${hours} hour${hours > 1 ? 's' : ''} before`
    } else {
      const days = Math.floor(minutes / 1440)
      return `${days} day${days > 1 ? 's' : ''} before`
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      'scheduled': { label: 'Scheduled', className: 'bg-blue-100 text-blue-800' },
      'completed': { label: 'Completed', className: 'bg-green-100 text-green-800' },
      'cancelled': { label: 'Cancelled', className: 'bg-red-100 text-red-800' },
    }

    const statusConfig = config[status as keyof typeof config]
    if (!statusConfig) return null

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.className}`}>
        {statusConfig.label}
      </span>
    )
  }

  return (
    <div 
      className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      onClick={handleBackdropClick}
    >
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Event Details
            </h3>
            <p className="text-sm text-gray-500">
              View complete event information
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Event Information */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-start justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">{event.title}</h4>
              <div className="flex items-center space-x-2">
                {getEventTypeBadge(event.eventType)}
                {getStatusBadge(event.status)}
              </div>
            </div>
            
            {event.description && (
              <p className="text-gray-700 whitespace-pre-wrap">{event.description}</p>
            )}
          </div>

          {/* Date and Time */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Date & Time</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Date</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(event.eventDate)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Time</label>
                <p className="mt-1 text-sm text-gray-900">{formatTime(event.eventTime, event.allDay)}</p>
              </div>
            </div>
          </div>

          {/* Additional Details */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Additional Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {event.location && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Location</label>
                  <p className="mt-1 text-sm text-gray-900">{event.location}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700">Reminder</label>
                <p className="mt-1 text-sm text-gray-900">{getReminderText(event.reminderMinutes)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(event.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(event.updatedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
            <Button
              variant="outline"
              onClick={onEdit}
            >
              Edit Event
            </Button>
            <Button
              variant="outline"
              onClick={onDelete}
              className="text-red-600 hover:text-red-700"
            >
              Delete Event
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
