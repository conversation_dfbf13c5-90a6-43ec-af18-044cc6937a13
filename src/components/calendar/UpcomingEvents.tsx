'use client'

import { But<PERSON> } from '@/components/ui/Button'

interface Event {
  id: number
  title: string
  description: string
  eventDate: Date
  eventTime: string
  eventType: string
  location: string
  allDay: boolean
  reminderMinutes: number | null
  status: string
  createdAt: Date
  updatedAt: Date
}

interface UpcomingEventsProps {
  events: Event[]
  loading: boolean
  onAddEvent: () => void
  onViewEvent: (event: Event) => void
  onEditEvent: (event: Event) => void
  onDeleteEvent: (event: Event) => void
}

const EVENT_TYPE_COLORS = {
  'appointment': 'bg-blue-100 text-blue-800',
  'breeding': 'bg-pink-100 text-pink-800',
  'health_check': 'bg-green-100 text-green-800',
  'show': 'bg-purple-100 text-purple-800',
  'training': 'bg-yellow-100 text-yellow-800',
  'other': 'bg-gray-100 text-gray-800',
}

const EVENT_TYPE_LABELS = {
  'appointment': 'Appointment',
  'breeding': 'Breeding',
  'health_check': 'Health Check',
  'show': 'Dog Show',
  'training': 'Training',
  'other': 'Other',
}

export type { Event }

export function UpcomingEvents({
  events,
  loading,
  onAddEvent,
  onViewEvent,
  onEditEvent,
  onDeleteEvent
}: UpcomingEventsProps) {
  const getEventTypeBadge = (type: string) => {
    const colorClass = EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] || EVENT_TYPE_COLORS.other
    const label = EVENT_TYPE_LABELS[type as keyof typeof EVENT_TYPE_LABELS] || 'Other'

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {label}
      </span>
    )
  }

  const formatDate = (date: Date) => {
    const eventDate = new Date(date)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    if (eventDate.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (eventDate.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow'
    } else {
      return eventDate.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      })
    }
  }

  const formatTime = (time: string, allDay: boolean) => {
    if (allDay) {
      return 'All day'
    }
    if (!time) {
      return ''
    }
    
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    
    return `${displayHour}:${minutes} ${ampm}`
  }

  const isEventToday = (date: Date) => {
    const eventDate = new Date(date)
    const today = new Date()
    return eventDate.toDateString() === today.toDateString()
  }

  const isEventOverdue = (date: Date, time: string, allDay: boolean) => {
    const now = new Date()
    const eventDate = new Date(date)
    
    if (allDay) {
      return eventDate < new Date(now.getFullYear(), now.getMonth(), now.getDate())
    }
    
    if (time) {
      const [hours, minutes] = time.split(':')
      eventDate.setHours(parseInt(hours), parseInt(minutes))
    }
    
    return eventDate < now
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Upcoming Events</h3>
          <Button
            size="sm"
            onClick={onAddEvent}
          >
            Add Event
          </Button>
        </div>
      </div>

      {/* Events List */}
      <div className="divide-y divide-gray-200">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-gray-500 mb-4">No upcoming events</p>
            <Button onClick={onAddEvent}>
              Add Your First Event
            </Button>
          </div>
        ) : (
          events.map((event) => (
            <div key={event.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {event.title}
                    </h4>
                    {getEventTypeBadge(event.eventType)}
                    {isEventToday(event.eventDate) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Today
                      </span>
                    )}
                    {isEventOverdue(event.eventDate, event.eventTime, event.allDay) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Overdue
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {formatDate(event.eventDate)}
                    </div>
                    
                    {(event.eventTime || event.allDay) && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {formatTime(event.eventTime, event.allDay)}
                      </div>
                    )}
                    
                    {event.location && (
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="truncate">{event.location}</span>
                      </div>
                    )}
                  </div>
                  
                  {event.description && (
                    <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                      {event.description}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewEvent(event)}
                  >
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditEvent(event)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onDeleteEvent(event)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      
      {events.length > 0 && (
        <div className="px-6 py-3 bg-gray-50 text-center">
          <p className="text-sm text-gray-500">
            Showing next {events.length} upcoming events
          </p>
        </div>
      )}
    </div>
  )
}
