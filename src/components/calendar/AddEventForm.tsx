'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'

interface AddEventFormProps {
  onSubmit: (data: EventFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
  initialDate?: string
  initialData?: Partial<EventFormData>
  isEditing?: boolean
}

interface EventFormData {
  title: string
  description: string
  eventDate: string
  eventTime: string
  eventType: string
  location: string
  allDay: boolean
  reminderMinutes: number | null
}

const EVENT_TYPES = [
  { value: 'appointment', label: 'Appointment' },
  { value: 'breeding', label: 'Breeding' },
  { value: 'health_check', label: 'Health Check' },
  { value: 'show', label: 'Dog Show' },
  { value: 'training', label: 'Training' },
  { value: 'other', label: 'Other' },
]

const REMINDER_OPTIONS = [
  { value: '', label: 'No reminder' },
  { value: '15', label: '15 minutes before' },
  { value: '30', label: '30 minutes before' },
  { value: '60', label: '1 hour before' },
  { value: '120', label: '2 hours before' },
  { value: '1440', label: '1 day before' },
  { value: '2880', label: '2 days before' },
]

export function AddEventForm({
  onSubmit,
  onCancel,
  loading = false,
  initialDate,
  initialData,
  isEditing = false
}: AddEventFormProps) {
  const [formData, setFormData] = useState<EventFormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    eventDate: initialData?.eventDate || initialDate || new Date().toISOString().split('T')[0],
    eventTime: initialData?.eventTime || '09:00',
    eventType: initialData?.eventType || 'appointment',
    location: initialData?.location || '',
    allDay: initialData?.allDay || false,
    reminderMinutes: initialData?.reminderMinutes || null,
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.title.trim()) {
      errors.title = 'Event title is required'
    }

    if (!formData.eventDate) {
      errors.eventDate = 'Event date is required'
    }

    if (!formData.allDay && formData.eventTime && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(formData.eventTime)) {
      errors.eventTime = 'Please enter a valid time (HH:MM format)'
    }

    if (!formData.eventType) {
      errors.eventType = 'Event type is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const submitData = {
      ...formData,
      reminderMinutes: formData.reminderMinutes ? Number(formData.reminderMinutes) : null
    }

    await onSubmit(submitData)
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        {isEditing ? 'Edit Event' : 'Add New Event'}
      </h3>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <Input
              label="Event Title *"
              name="title"
              value={formData.title}
              onChange={handleChange}
              error={formErrors.title}
              required
              placeholder="Enter event title"
            />
          </div>

          <Input
            label="Date *"
            name="eventDate"
            type="date"
            value={formData.eventDate}
            onChange={handleChange}
            error={formErrors.eventDate}
            required
          />

          <Select
            label="Event Type *"
            name="eventType"
            value={formData.eventType}
            onChange={handleChange}
            options={EVENT_TYPES}
            error={formErrors.eventType}
            required
          />
        </div>

        {/* Time Settings */}
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="allDay"
              name="allDay"
              checked={formData.allDay}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="allDay" className="ml-2 block text-sm text-gray-900">
              All day event
            </label>
          </div>

          {!formData.allDay && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Time"
                name="eventTime"
                type="time"
                value={formData.eventTime}
                onChange={handleChange}
                error={formErrors.eventTime}
              />

              <Select
                label="Reminder"
                name="reminderMinutes"
                value={formData.reminderMinutes?.toString() || ''}
                onChange={handleChange}
                options={REMINDER_OPTIONS}
              />
            </div>
          )}
        </div>

        {/* Location */}
        <Input
          label="Location"
          name="location"
          value={formData.location}
          onChange={handleChange}
          error={formErrors.location}
          placeholder="Enter event location"
        />

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500"
            placeholder="Add event description or notes..."
          />
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            {isEditing ? 'Update Event' : 'Add Event'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export { EventFormData }
