'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { PriceInput } from '@/components/puppies/PriceInput'

interface BulkPriceUpdateProps {
  currentPuppyCount: number
  onUpdate: (price: number) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function BulkPriceUpdate({
  currentPuppyCount,
  onUpdate,
  onCancel,
  loading = false
}: BulkPriceUpdateProps) {
  const [price, setPrice] = useState<number | undefined>(undefined)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (price === undefined || price <= 0) {
      setError('Please enter a valid price')
      return
    }

    setError('')
    await onUpdate(price)
  }

  const handlePriceChange = (newPrice: number | undefined) => {
    setPrice(newPrice)
    if (error) {
      setError('')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Update All Puppy Prices
      </h3>
      
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">
              Bulk Price Update
            </h4>
            <p className="text-sm text-blue-700 mt-1">
              This will update the price for all {currentPuppyCount} puppies in this litter. 
              Individual puppy prices can still be modified later.
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <PriceInput
          label="Price per Puppy"
          value={price}
          onChange={handlePriceChange}
          error={error}
          placeholder="Enter price (e.g., 1500.00)"
          required
          disabled={loading}
        />

        {price !== undefined && price > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Price Summary</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Price per puppy:</span>
                <span className="font-medium">{formatCurrency(price)}</span>
              </div>
              <div className="flex justify-between">
                <span>Number of puppies:</span>
                <span className="font-medium">{currentPuppyCount}</span>
              </div>
              <div className="border-t pt-1 mt-2 flex justify-between font-medium text-gray-900">
                <span>Total litter value:</span>
                <span>{formatCurrency(price * currentPuppyCount)}</span>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            disabled={price === undefined || price <= 0}
          >
            Update All Prices
          </Button>
        </div>
      </form>
    </div>
  )
}
