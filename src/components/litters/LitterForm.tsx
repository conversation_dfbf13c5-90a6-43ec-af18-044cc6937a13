'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import { ImageUpload } from '@/components/ui/ImageUpload'

interface LitterFormData {
  litterCode: string
  minExpectedSizeLbs: string
  maxExpectedSizeLbs: string
  expectedBirthDate: string
  actualBirthDate: string
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color: string
  momId: string
  dadId: string
}

interface BreedingDog {
  id: number
  name: string
}

interface Photo {
  id: number
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: Date
}

interface LitterFormProps {
  initialData?: Partial<LitterFormData>
  onSubmit: (data: LitterFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
  submitLabel?: string
  litterId?: number // For image upload when editing existing litter
}

export function LitterForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  submitLabel = 'Save Litter',
  litterId
}: LitterFormProps) {
  const [formData, setFormData] = useState<LitterFormData>({
    litterCode: '',
    minExpectedSizeLbs: '',
    maxExpectedSizeLbs: '',
    expectedBirthDate: '',
    actualBirthDate: '',
    status: 'NOT_BORN',
    color: '',
    momId: '',
    dadId: '',
    ...initialData
  })

  const [breedingDogs, setBreedingDogs] = useState<BreedingDog[]>([])
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loadingPhotos, setLoadingPhotos] = useState(false)

  // Load breeding dogs on component mount
  useEffect(() => {
    const loadBreedingDogs = async () => {
      try {
        const response = await fetch('/api/breeding-dogs')
        if (response.ok) {
          const data = await response.json()
          setBreedingDogs(data.breedingDogs || [])
        }
      } catch (error) {
        console.error('Failed to load breeding dogs:', error)
      }
    }

    loadBreedingDogs()
  }, [])

  // Load photos for existing litter
  useEffect(() => {
    if (litterId) {
      const loadPhotos = async () => {
        try {
          setLoadingPhotos(true)
          const response = await fetch(`/api/photos?entityType=litter&entityId=${litterId}`)
          if (response.ok) {
            const data = await response.json()
            setPhotos(data.photos || [])
          }
        } catch (error) {
          console.error('Error loading photos:', error)
        } finally {
          setLoadingPhotos(false)
        }
      }

      loadPhotos()
    }
  }, [litterId])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.litterCode.trim()) {
      errors.litterCode = 'Litter code is required'
    }

    if (formData.minExpectedSizeLbs && formData.maxExpectedSizeLbs) {
      const min = parseFloat(formData.minExpectedSizeLbs)
      const max = parseFloat(formData.maxExpectedSizeLbs)
      if (min > max) {
        errors.maxExpectedSizeLbs = 'Maximum size must be greater than minimum size'
      }
    }

    if (formData.expectedBirthDate && formData.actualBirthDate) {
      const expected = new Date(formData.expectedBirthDate)
      const actual = new Date(formData.actualBirthDate)
      if (actual < expected) {
        errors.actualBirthDate = 'Actual birth date cannot be before expected birth date'
      }
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    await onSubmit(formData)
  }

  const statusOptions = [
    { value: 'NOT_BORN', label: 'Not Born' },
    { value: 'BORN', label: 'Born' },
    { value: 'READY_TO_GO_HOME', label: 'Ready to Go Home' },
  ]

  const dogOptions = [
    { value: '', label: 'Select a dog...' },
    ...breedingDogs.map(dog => ({ value: dog.id.toString(), label: dog.name }))
  ]

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Litter Code *"
            name="litterCode"
            value={formData.litterCode}
            onChange={handleChange}
            error={formErrors.litterCode}
            required
            placeholder="e.g., LT2024-001"
          />

          <Select
            label="Status *"
            name="status"
            value={formData.status}
            onChange={handleChange}
            options={statusOptions}
            error={formErrors.status}
            required
          />

          <Input
            label="Color"
            name="color"
            value={formData.color}
            onChange={handleChange}
            error={formErrors.color}
            placeholder="e.g., Golden, Black, Mixed"
          />

          <div></div> {/* Empty div for grid spacing */}

          <Input
            label="Min Expected Size (lbs)"
            name="minExpectedSizeLbs"
            type="number"
            step="0.1"
            value={formData.minExpectedSizeLbs}
            onChange={handleChange}
            error={formErrors.minExpectedSizeLbs}
            placeholder="e.g., 45.0"
          />

          <Input
            label="Max Expected Size (lbs)"
            name="maxExpectedSizeLbs"
            type="number"
            step="0.1"
            value={formData.maxExpectedSizeLbs}
            onChange={handleChange}
            error={formErrors.maxExpectedSizeLbs}
            placeholder="e.g., 65.0"
          />

          <Input
            label="Expected Birth Date"
            name="expectedBirthDate"
            type="date"
            value={formData.expectedBirthDate}
            onChange={handleChange}
            error={formErrors.expectedBirthDate}
          />

          <Input
            label="Actual Birth Date"
            name="actualBirthDate"
            type="date"
            value={formData.actualBirthDate}
            onChange={handleChange}
            error={formErrors.actualBirthDate}
          />

          <Select
            label="Mother"
            name="momId"
            value={formData.momId}
            onChange={handleChange}
            options={dogOptions}
            error={formErrors.momId}
          />

          <Select
            label="Father"
            name="dadId"
            value={formData.dadId}
            onChange={handleChange}
            options={dogOptions}
            error={formErrors.dadId}
          />
        </div>

        {/* Image Upload Section */}
        {litterId && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Litter Photos</h3>
            {loadingPhotos ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <ImageUpload
                entityType="litter"
                entityId={litterId}
                photos={photos}
                onPhotosChange={setPhotos}
                maxFiles={20}
                disabled={loading}
              />
            )}
          </div>
        )}

        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            {submitLabel}
          </Button>
        </div>
      </form>
    </div>
  )
}
