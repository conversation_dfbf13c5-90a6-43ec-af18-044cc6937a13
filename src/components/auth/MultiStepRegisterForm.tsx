'use client'

import React, { useState } from 'react'
import { useUser, RegisterData } from '@/contexts/UserContext'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'

interface MultiStepRegisterFormProps {
  onSuccess?: () => void
  onSwitchToLogin?: () => void
}

interface Step1Data {
  email: string
  password: string
  confirmPassword: string
  userType: 'customer' | 'breeder'
}

interface Step2Data {
  firstName: string
  lastName: string
  phone: string
  streetAddress: string
  aptNumber: string
  city: string
  state: string
  zipCode: string
}

interface Step3Data {
  businessName: string
  businessPhone: string
  businessStreetAddress: string
  businessAptNumber: string
  businessCity: string
  businessState: string
  businessZipCode: string
}

const US_STATES = [
  { value: '', label: 'Select State' },
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
]

export function MultiStepRegisterForm({ onSuccess, onSwitchToLogin }: MultiStepRegisterFormProps) {
  const { register, loading, error, clearError } = useUser()
  const [currentStep, setCurrentStep] = useState(1)
  
  const [step1Data, setStep1Data] = useState<Step1Data>({
    email: '',
    password: '',
    confirmPassword: '',
    userType: 'customer'
  })
  
  const [step2Data, setStep2Data] = useState<Step2Data>({
    firstName: '',
    lastName: '',
    phone: '',
    streetAddress: '',
    aptNumber: '',
    city: '',
    state: '',
    zipCode: ''
  })

  const [step3Data, setStep3Data] = useState<Step3Data>({
    businessName: '',
    businessPhone: '',
    businessStreetAddress: '',
    businessAptNumber: '',
    businessCity: '',
    businessState: '',
    businessZipCode: ''
  })

  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  const handleStep1Change = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setStep1Data(prev => ({ ...prev, [name]: value }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
    
    // Clear general error
    if (error) {
      clearError()
    }
  }

  const handleStep2Change = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setStep2Data(prev => ({ ...prev, [name]: value }))

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }

    // Clear general error
    if (error) {
      clearError()
    }
  }

  const handleStep3Change = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setStep3Data(prev => ({ ...prev, [name]: value }))

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }

    // Clear general error
    if (error) {
      clearError()
    }
  }

  const validateStep1 = () => {
    const errors: Record<string, string> = {}

    if (!step1Data.email) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(step1Data.email)) {
      errors.email = 'Email is invalid'
    }

    if (!step1Data.password) {
      errors.password = 'Password is required'
    } else if (step1Data.password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }

    if (!step1Data.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (step1Data.password !== step1Data.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    if (!step1Data.userType) {
      errors.userType = 'Please select an account type'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validateStep2 = () => {
    const errors: Record<string, string> = {}

    if (!step2Data.firstName) {
      errors.firstName = 'First name is required'
    }

    if (!step2Data.lastName) {
      errors.lastName = 'Last name is required'
    }

    if (!step2Data.streetAddress) {
      errors.streetAddress = 'Street address is required'
    }

    if (!step2Data.city) {
      errors.city = 'City is required'
    }

    if (!step2Data.state) {
      errors.state = 'State is required'
    }

    if (!step2Data.zipCode) {
      errors.zipCode = 'ZIP code is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validateStep3 = () => {
    const errors: Record<string, string> = {}

    if (!step3Data.businessName) {
      errors.businessName = 'Business name is required'
    }

    if (!step3Data.businessStreetAddress) {
      errors.businessStreetAddress = 'Business street address is required'
    }

    if (!step3Data.businessCity) {
      errors.businessCity = 'Business city is required'
    }

    if (!step3Data.businessState) {
      errors.businessState = 'Business state is required'
    }

    if (!step3Data.businessZipCode) {
      errors.businessZipCode = 'Business ZIP code is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleStep1Submit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateStep1()) {
      setCurrentStep(2)
    }
  }

  const handleStep2Submit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep2()) {
      return
    }

    // For customers, complete registration
    if (step1Data.userType === 'customer') {
      handleCustomerRegistration()
    } else {
      // For breeders, go to step 3
      setCurrentStep(3)
    }
  }

  const handleCustomerRegistration = async () => {
    const registerData: RegisterData = {
      email: step1Data.email,
      password: step1Data.password,
      userType: step1Data.userType,
      firstName: step2Data.firstName,
      lastName: step2Data.lastName,
      phone: step2Data.phone || undefined,
      streetAddress: step2Data.streetAddress,
      aptNumber: step2Data.aptNumber || undefined,
      city: step2Data.city,
      state: step2Data.state,
      zipCode: step2Data.zipCode,
    }

    const success = await register(registerData)
    if (success && onSuccess) {
      onSuccess()
    }
  }

  const handleStep3Submit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep3()) {
      return
    }

    const registerData: RegisterData = {
      email: step1Data.email,
      password: step1Data.password,
      userType: step1Data.userType,
      firstName: step2Data.firstName,
      lastName: step2Data.lastName,
      phone: step2Data.phone || undefined,
      streetAddress: step2Data.streetAddress,
      aptNumber: step2Data.aptNumber || undefined,
      city: step2Data.city,
      state: step2Data.state,
      zipCode: step2Data.zipCode,
      businessName: step3Data.businessName,
      businessPhone: step3Data.businessPhone || undefined,
      businessStreetAddress: step3Data.businessStreetAddress,
      businessAptNumber: step3Data.businessAptNumber || undefined,
      businessCity: step3Data.businessCity,
      businessState: step3Data.businessState,
      businessZipCode: step3Data.businessZipCode,
    }

    const success = await register(registerData)
    if (success && onSuccess) {
      onSuccess()
    }
  }

  const handleBackToStep1 = () => {
    setCurrentStep(1)
    setFormErrors({})
  }

  const handleBackToStep2 = () => {
    setCurrentStep(2)
    setFormErrors({})
  }

  const userTypeOptions = [
    { value: 'customer', label: 'Customer' },
    { value: 'breeder', label: 'Breeder' },
  ]

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        {/* Progress indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className={`text-sm font-medium ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
              Account Details
            </span>
            <span className={`text-sm font-medium ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
              Personal Info
            </span>
            {step1Data.userType === 'breeder' && (
              <span className={`text-sm font-medium ${currentStep >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>
                Business Info
              </span>
            )}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${(currentStep / (step1Data.userType === 'breeder' ? 3 : 2)) * 100}%`
              }}
            ></div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">
          {currentStep === 1 ? 'Create Account' :
           currentStep === 2 ? 'Personal Information' :
           'Business Information'}
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {currentStep === 1 ? (
          <form onSubmit={handleStep1Submit} className="space-y-4">
            <Select
              label="Account Type"
              name="userType"
              value={step1Data.userType}
              onChange={handleStep1Change}
              options={userTypeOptions}
              error={formErrors.userType}
              required
            />

            <Input
              label="Email Address"
              type="email"
              name="email"
              value={step1Data.email}
              onChange={handleStep1Change}
              error={formErrors.email}
              required
              autoComplete="email"
            />

            <Input
              label="Password"
              type="password"
              name="password"
              value={step1Data.password}
              onChange={handleStep1Change}
              error={formErrors.password}
              required
              autoComplete="new-password"
            />

            <Input
              label="Confirm Password"
              type="password"
              name="confirmPassword"
              value={step1Data.confirmPassword}
              onChange={handleStep1Change}
              error={formErrors.confirmPassword}
              required
              autoComplete="new-password"
            />

            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              Continue
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={onSwitchToLogin}
                className="text-blue-600 hover:text-blue-500 text-sm"
              >
                Already have an account? Sign in
              </button>
            </div>
          </form>
        ) : currentStep === 2 ? (
          <form onSubmit={handleStep2Submit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                name="firstName"
                value={step2Data.firstName}
                onChange={handleStep2Change}
                error={formErrors.firstName}
                required
              />

              <Input
                label="Last Name"
                type="text"
                name="lastName"
                value={step2Data.lastName}
                onChange={handleStep2Change}
                error={formErrors.lastName}
                required
              />
            </div>

            <Input
              label="Phone Number"
              type="tel"
              name="phone"
              value={step2Data.phone}
              onChange={handleStep2Change}
              error={formErrors.phone}
              placeholder="(*************"
            />

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Address Information</h3>
              
              <Input
                label="Street Address"
                type="text"
                name="streetAddress"
                value={step2Data.streetAddress}
                onChange={handleStep2Change}
                error={formErrors.streetAddress}
                required
              />

              <Input
                label="Apartment/Unit Number"
                type="text"
                name="aptNumber"
                value={step2Data.aptNumber}
                onChange={handleStep2Change}
                error={formErrors.aptNumber}
                placeholder="Optional"
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="City"
                  type="text"
                  name="city"
                  value={step2Data.city}
                  onChange={handleStep2Change}
                  error={formErrors.city}
                  required
                />

                <div className="grid grid-cols-2 gap-2">
                  <Select
                    label="State"
                    name="state"
                    value={step2Data.state}
                    onChange={handleStep2Change}
                    options={US_STATES}
                    error={formErrors.state}
                    required
                  />

                  <Input
                    label="ZIP Code"
                    type="text"
                    name="zipCode"
                    value={step2Data.zipCode}
                    onChange={handleStep2Change}
                    error={formErrors.zipCode}
                    required
                    placeholder="12345"
                  />
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleBackToStep1}
                className="flex-1"
              >
                Back
              </Button>
              
              <Button
                type="submit"
                className="flex-1"
                disabled={loading}
              >
                {step1Data.userType === 'customer'
                  ? (loading ? 'Creating Account...' : 'Create Account')
                  : 'Continue'
                }
              </Button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleStep3Submit} className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Business Information</h3>

              <Input
                label="Business Name"
                type="text"
                name="businessName"
                value={step3Data.businessName}
                onChange={handleStep3Change}
                error={formErrors.businessName}
                required
                placeholder="ABC Dog Breeding"
              />

              <Input
                label="Business Phone"
                type="tel"
                name="businessPhone"
                value={step3Data.businessPhone}
                onChange={handleStep3Change}
                error={formErrors.businessPhone}
                placeholder="(*************"
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Business Address</h3>

              <Input
                label="Street Address"
                type="text"
                name="businessStreetAddress"
                value={step3Data.businessStreetAddress}
                onChange={handleStep3Change}
                error={formErrors.businessStreetAddress}
                required
              />

              <Input
                label="Apartment/Unit Number"
                type="text"
                name="businessAptNumber"
                value={step3Data.businessAptNumber}
                onChange={handleStep3Change}
                error={formErrors.businessAptNumber}
                placeholder="Optional"
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="City"
                  type="text"
                  name="businessCity"
                  value={step3Data.businessCity}
                  onChange={handleStep3Change}
                  error={formErrors.businessCity}
                  required
                />

                <div className="grid grid-cols-2 gap-2">
                  <Select
                    label="State"
                    name="businessState"
                    value={step3Data.businessState}
                    onChange={handleStep3Change}
                    options={US_STATES}
                    error={formErrors.businessState}
                    required
                  />

                  <Input
                    label="ZIP Code"
                    type="text"
                    name="businessZipCode"
                    value={step3Data.businessZipCode}
                    onChange={handleStep3Change}
                    error={formErrors.businessZipCode}
                    required
                    placeholder="12345"
                  />
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleBackToStep2}
                className="flex-1"
              >
                Back
              </Button>

              <Button
                type="submit"
                className="flex-1"
                disabled={loading}
              >
                {loading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}
