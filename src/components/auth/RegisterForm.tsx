'use client'

import React, { useState } from 'react'
import { useUser, RegisterData } from '@/contexts/UserContext'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'

interface RegisterFormProps {
  onSuccess?: () => void
  onSwitchToLogin?: () => void
}

export function RegisterForm({ onSuccess, onSwitchToLogin }: RegisterFormProps) {
  const { register, loading, error, clearError } = useUser()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    userType: 'customer' as 'customer' | 'breeder',
    firstName: '',
    lastName: '',
    phone: '',
    streetAddress: '',
    aptNumber: '',
    city: '',
    state: '',
    zipCode: '',
    businessName: '',
    businessPhone: '',
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
    
    // Clear general error
    if (error) {
      clearError()
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.email) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (!formData.password) {
      errors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters long'
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    if (!formData.firstName) {
      errors.firstName = 'First name is required'
    }

    if (!formData.lastName) {
      errors.lastName = 'Last name is required'
    }

    if (!formData.streetAddress) {
      errors.streetAddress = 'Street address is required'
    }

    if (!formData.city) {
      errors.city = 'City is required'
    }

    if (!formData.state) {
      errors.state = 'State is required'
    }

    if (!formData.zipCode) {
      errors.zipCode = 'ZIP code is required'
    } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipCode)) {
      errors.zipCode = 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789)'
    }

    if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      errors.phone = 'Please enter a valid phone number'
    }

    if (formData.userType === 'breeder' && !formData.businessName) {
      errors.businessName = 'Business name is required for breeders'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const registerData: RegisterData = {
      email: formData.email,
      password: formData.password,
      userType: formData.userType,
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone || undefined,
      streetAddress: formData.streetAddress,
      aptNumber: formData.aptNumber || undefined,
      city: formData.city,
      state: formData.state,
      zipCode: formData.zipCode,
      businessName: formData.userType === 'breeder' ? formData.businessName : undefined,
      businessPhone: formData.userType === 'breeder' ? formData.businessPhone : undefined,
    }

    const success = await register(registerData)
    if (success && onSuccess) {
      onSuccess()
    }
  }

  const userTypeOptions = [
    { value: 'customer', label: 'Customer' },
    { value: 'breeder', label: 'Breeder' },
  ]

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">
          Create Account
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <Select
            label="Account Type"
            name="userType"
            value={formData.userType}
            onChange={handleChange}
            options={userTypeOptions}
            error={formErrors.userType}
            required
          />

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="First Name"
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              error={formErrors.firstName}
              required
            />

            <Input
              label="Last Name"
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              error={formErrors.lastName}
              required
            />
          </div>

          <Input
            label="Email Address"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            error={formErrors.email}
            required
            autoComplete="email"
          />

          <Input
            label="Password"
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            error={formErrors.password}
            required
            autoComplete="new-password"
            helperText="Must be at least 8 characters long"
          />

          <Input
            label="Confirm Password"
            type="password"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            error={formErrors.confirmPassword}
            required
            autoComplete="new-password"
          />

          <Input
            label="Phone Number"
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            error={formErrors.phone}
            placeholder="(*************"
          />

          <Input
            label="Street Address"
            type="text"
            name="streetAddress"
            value={formData.streetAddress}
            onChange={handleChange}
            error={formErrors.streetAddress}
            placeholder="123 Main Street"
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Apartment/Unit"
              type="text"
              name="aptNumber"
              value={formData.aptNumber}
              onChange={handleChange}
              error={formErrors.aptNumber}
              placeholder="Apt 4B"
            />

            <Input
              label="City"
              type="text"
              name="city"
              value={formData.city}
              onChange={handleChange}
              error={formErrors.city}
              placeholder="New York"
              required
            />

            <div className="grid grid-cols-2 gap-2">
              <Input
                label="State"
                type="text"
                name="state"
                value={formData.state}
                onChange={handleChange}
                error={formErrors.state}
                placeholder="NY"
                required
              />

              <Input
                label="ZIP Code"
                type="text"
                name="zipCode"
                value={formData.zipCode}
                onChange={handleChange}
                error={formErrors.zipCode}
                placeholder="12345"
                required
              />
            </div>
          </div>

          {formData.userType === 'breeder' && (
            <>
              <Input
                label="Business Name"
                type="text"
                name="businessName"
                value={formData.businessName}
                onChange={handleChange}
                error={formErrors.businessName}
                required
              />

              <Input
                label="Business Phone"
                type="tel"
                name="businessPhone"
                value={formData.businessPhone}
                onChange={handleChange}
                error={formErrors.businessPhone}
                helperText="Optional"
              />
            </>
          )}

          <Button
            type="submit"
            className="w-full"
            loading={loading}
          >
            Create Account
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              Sign in here
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
