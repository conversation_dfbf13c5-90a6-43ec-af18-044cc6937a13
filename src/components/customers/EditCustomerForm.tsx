'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'

interface Customer {
  relationshipId: number
  customerId: number
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    aptNumber?: string
    city: string
    state: string
    zipCode: string
  }
  relationshipType: 'owner' | 'lead'
  status: 'active' | 'inactive'
  notes: string
  customerCreatedAt: Date
  relationshipCreatedAt: Date
  relationshipUpdatedAt: Date
}

interface EditCustomerFormProps {
  customer: Customer
  onSubmit: (data: {
    relationshipType: string;
    status: string;
    notes: string;
    firstName: string;
    lastName: string;
    phone: string;
    streetAddress: string;
    aptNumber: string;
    city: string;
    state: string;
    zipCode: string;
  }) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

const RELATIONSHIP_TYPES = [
  { value: 'lead', label: 'Lead' },
  { value: 'owner', label: 'Owner' },
]

const STATUS_OPTIONS = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
]

const US_STATES = [
  { value: '', label: 'Select State' },
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
]

export function EditCustomerForm({ customer, onSubmit, onCancel, loading = false }: EditCustomerFormProps) {
  const [formData, setFormData] = useState({
    // Customer details
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phone,
    streetAddress: customer.address.street,
    aptNumber: customer.address.aptNumber || '',
    city: customer.address.city,
    state: customer.address.state,
    zipCode: customer.address.zipCode,
    // Relationship details
    relationshipType: customer.relationshipType,
    status: customer.status,
    notes: customer.notes,
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required'
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required'
    }

    if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      errors.phone = 'Please enter a valid phone number'
    }

    if (formData.zipCode && !/^\d{5}(-\d{4})?$/.test(formData.zipCode)) {
      errors.zipCode = 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789)'
    }

    if (!formData.relationshipType) {
      errors.relationshipType = 'Relationship type is required'
    }

    if (!formData.status) {
      errors.status = 'Status is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    await onSubmit(formData)
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        Edit Customer Relationship: {customer.firstName} {customer.lastName}
      </h3>
      
      {/* Customer Information */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-900 mb-4">Customer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="First Name *"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            error={formErrors.firstName}
            required
          />

          <Input
            label="Last Name *"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            error={formErrors.lastName}
            required
          />

          <div className="md:col-span-2">
            <div className="p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Email:</span>{' '}
              <span className="text-sm text-gray-900">{customer.email}</span>
              <div className="text-xs text-gray-500 mt-1">
                Email cannot be changed. Customer since {new Date(customer.customerCreatedAt).toLocaleDateString()}
              </div>
            </div>
          </div>

          <Input
            label="Phone Number"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleChange}
            error={formErrors.phone}
            placeholder="(*************"
          />
        </div>
      </div>

      {/* Address Information */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-900 mb-4">Address Information</h4>

        <Input
          label="Street Address"
          name="streetAddress"
          value={formData.streetAddress}
          onChange={handleChange}
          error={formErrors.streetAddress}
          placeholder="123 Main Street"
          className="mb-4"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="Apartment/Unit"
            name="aptNumber"
            value={formData.aptNumber}
            onChange={handleChange}
            error={formErrors.aptNumber}
            placeholder="Apt 4B"
          />

          <Input
            label="City"
            name="city"
            value={formData.city}
            onChange={handleChange}
            error={formErrors.city}
            placeholder="New York"
          />

          <div className="grid grid-cols-2 gap-2">
            <Select
              label="State"
              name="state"
              value={formData.state}
              onChange={handleChange}
              options={US_STATES}
              error={formErrors.state}
            />

            <Input
              label="ZIP Code"
              name="zipCode"
              value={formData.zipCode}
              onChange={handleChange}
              error={formErrors.zipCode}
              placeholder="12345"
            />
          </div>
        </div>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Relationship Settings */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Relationship Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Select
              label="Relationship Type *"
              name="relationshipType"
              value={formData.relationshipType}
              onChange={handleChange}
              options={RELATIONSHIP_TYPES}
              error={formErrors.relationshipType}
              required
            />

            <Select
              label="Status *"
              name="status"
              value={formData.status}
              onChange={handleChange}
              options={STATUS_OPTIONS}
              error={formErrors.status}
              required
            />
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes
          </label>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500"
            placeholder="Additional notes about this customer relationship..."
          />
        </div>

        {/* Relationship History */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="text-md font-medium text-gray-900 mb-2">Relationship History</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>
              <span className="font-medium">Added:</span>{' '}
              {new Date(customer.relationshipCreatedAt).toLocaleDateString()}
            </div>
            <div>
              <span className="font-medium">Last Updated:</span>{' '}
              {new Date(customer.relationshipUpdatedAt).toLocaleDateString()}
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            Update Relationship
          </Button>
        </div>
      </form>
    </div>
  )
}
