'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/Button'

interface Customer {
  relationshipId: number
  customerId: number
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    aptNumber?: string
    city: string
    state: string
    zipCode: string
  }
  relationshipType: 'owner' | 'lead'
  status: 'active' | 'inactive'
  notes: string
  customerCreatedAt: Date
  relationshipCreatedAt: Date
  relationshipUpdatedAt: Date
}

interface CustomerDetailModalProps {
  customer: Customer
  onClose: () => void
  onEdit: () => void
}

export function CustomerDetailModal({ customer, onClose, onEdit }: CustomerDetailModalProps) {
  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [onClose])

  const getRelationshipTypeBadge = (type: string) => {
    const config = {
      'owner': { label: 'Owner', className: 'bg-green-100 text-green-800' },
      'lead': { label: 'Lead', className: 'bg-blue-100 text-blue-800' },
    }

    const typeConfig = config[type as keyof typeof config]
    if (!typeConfig) return null

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig.className}`}>
        {typeConfig.label}
      </span>
    )
  }

  const getStatusBadge = (status: string) => {
    const config = {
      'active': { label: 'Active', className: 'bg-green-100 text-green-800' },
      'inactive': { label: 'Inactive', className: 'bg-gray-100 text-gray-800' },
    }

    const statusConfig = config[status as keyof typeof config]
    if (!statusConfig) return null

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.className}`}>
        {statusConfig.label}
      </span>
    )
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatAddress = () => {
    const parts = []
    if (customer.address.street) parts.push(customer.address.street)
    if (customer.address.aptNumber) parts.push(`Apt ${customer.address.aptNumber}`)
    if (customer.address.city) parts.push(customer.address.city)
    if (customer.address.state) parts.push(customer.address.state)
    if (customer.address.zipCode) parts.push(customer.address.zipCode)
    
    return parts.length > 0 ? parts.join(', ') : 'No address provided'
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div
      className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      onClick={handleBackdropClick}
    >
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Customer Details
            </h3>
            <p className="text-sm text-gray-500">
              View complete customer information and relationship details
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Customer Information */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Personal Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Full Name</label>
                <p className="mt-1 text-sm text-gray-900">{customer.firstName} {customer.lastName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email Address</label>
                <p className="mt-1 text-sm text-gray-900">{customer.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                <p className="mt-1 text-sm text-gray-900">{customer.phone || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Customer Since</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(customer.customerCreatedAt)}</p>
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Address Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Street Address</label>
                <p className="mt-1 text-sm text-gray-900">{customer.address.street || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Apartment/Unit</label>
                <p className="mt-1 text-sm text-gray-900">{customer.address.aptNumber || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">City</label>
                <p className="mt-1 text-sm text-gray-900">{customer.address.city || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">State</label>
                <p className="mt-1 text-sm text-gray-900">{customer.address.state || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">ZIP Code</label>
                <p className="mt-1 text-sm text-gray-900">{customer.address.zipCode || 'Not provided'}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700">Full Address</label>
                <p className="mt-1 text-sm text-gray-900">{formatAddress()}</p>
              </div>
            </div>
          </div>

          {/* Relationship Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Relationship Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Relationship Type</label>
                <div className="mt-1">
                  {getRelationshipTypeBadge(customer.relationshipType)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1">
                  {getStatusBadge(customer.status)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Added to Your List</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(customer.relationshipCreatedAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(customer.relationshipUpdatedAt)}</p>
              </div>
              {customer.notes && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">Notes</label>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{customer.notes}</p>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
            <Button
              onClick={onEdit}
            >
              Edit Customer
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
