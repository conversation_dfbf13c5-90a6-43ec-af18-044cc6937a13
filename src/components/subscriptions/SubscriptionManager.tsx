'use client'

import { useState, useEffect } from 'react'

// Define subscription plans directly to avoid import issues
const SUBSCRIPTION_PLANS = {
  starter: {
    name: 'Starter',
    priceMonthly: 1900, // $19.00 in cents
    priceYearly: 19000, // $190.00 in cents (2 months free)
    maxLitters: 2,
    maxPuppies: 20,
    maxCustomers: 50,
    maxPhotos: 30,
    maxDocuments: 30,
    features: [
      'Up to 2 litters',
      'Up to 20 puppies',
      'Customer management (50 customers and leads)',
      'Photo storage (30 photos)',
      'Document storage (30 documents)',
      'Basic public website',
      'Calendar & event tracking'
    ]
  },
  professional: {
    name: 'Professional',
    priceMonthly: 4900, // $49.00 in cents
    priceYearly: 49000, // $490.00 in cents (2 months free)
    maxLitters: 10,
    maxPuppies: 100,
    maxCustomers: 150,
    maxPhotos: 200,
    maxDocuments: 200,
    features: [
      'Everything in Starter, plus:',
      'Up to 10 litters',
      'Up to 100 puppies',
      'Customer management (150 customers and leads)',
      'Photo storage (200 photos)',
      'Document storage (200 documents)',
      'Custom branding & logo',
      'Advanced public website',
      'Custom domain support',
      'Detailed analytics & reporting',
      'Waiting list management',
      'Customer relationship tracking'
    ]
  },
  premium: {
    name: 'Premium',
    priceMonthly: 9900, // $99.00 in cents
    priceYearly: 99000, // $990.00 in cents (2 months free)
    maxLitters: null, // unlimited
    maxPuppies: null, // unlimited
    maxCustomers: null, // unlimited
    maxPhotos: 1000,
    maxDocuments: 1000,
    features: [
      'Everything in Professional, plus:',
      'Unlimited litters',
      'Unlimited puppies',
      'Unlimited customers',
      'Photo storage (1000 photos)',
      'Document storage (1000 documents)',
      'Automated email notifications',
      'Pedigree tracking'
    ]
  }
} as const

interface Subscription {
  id: number
  status: string
  current_period_start: string
  current_period_end: string
  trial_end: string | null
  canceled_at: string | null
  plan_name: string
  price_monthly: number
  price_yearly: number
  max_litters: number | null
  max_puppies: number | null
  max_customers: number | null
  max_photos: number | null
  max_documents: number | null
  features: string[]
}

interface Usage {
  litters: number
  puppies: number
  customers: number
  photos: number
  documents: number
}

export function SubscriptionManager() {
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [usage, setUsage] = useState<Usage | null>(null)
  const [loading, setLoading] = useState(true)
  const [upgrading, setUpgrading] = useState(false)

  useEffect(() => {
    loadSubscription()
  }, [])

  const loadSubscription = async () => {
    try {
      const response = await fetch('/api/subscriptions/current')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      setSubscription(data.subscription)
      setUsage(data.usage)
    } catch (error) {
      console.error('Failed to load subscription:', error)
      // Set default values on error
      setSubscription(null)
      setUsage(null)
    } finally {
      setLoading(false)
    }
  }

  const handleUpgrade = async (planType: string, interval: 'monthly' | 'yearly') => {
    setUpgrading(true)
    try {
      const response = await fetch('/api/subscriptions/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planType, interval }),
      })

      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      }
    } catch (error) {
      console.error('Failed to create checkout session:', error)
    } finally {
      setUpgrading(false)
    }
  }

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(0)}`
  }

  const getUsagePercentage = (used: number, limit: number | null) => {
    if (limit === null) return 0 // unlimited
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100'
    if (percentage >= 75) return 'text-yellow-600 bg-yellow-100'
    return 'text-green-600 bg-green-100'
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Subscription Management</h2>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!subscription) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Choose Your Plan</h2>
        <p className="text-gray-600 mb-6">
          Start your 14-day free trial with any plan. No credit card required.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6">
          {Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => (
            <div key={key} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
              <h3 className="text-lg font-semibold mb-2">{plan.name}</h3>
              <div className="mb-4">
                <span className="text-3xl font-bold">{formatPrice(plan.priceMonthly)}</span>
                <span className="text-gray-600">/month</span>
              </div>
              <ul className="space-y-2 mb-6 text-sm">
                {plan.features.slice(0, 5).map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
              <div className="space-y-2">
                <button
                  onClick={() => handleUpgrade(key, 'monthly')}
                  disabled={upgrading}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  Start Monthly Trial
                </button>
                <button
                  onClick={() => handleUpgrade(key, 'yearly')}
                  disabled={upgrading}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  Start Yearly Trial (Save 2 months!)
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Current Plan</h2>
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-medium capitalize">{subscription.plan_name}</h3>
            <p className="text-gray-600">
              {formatPrice(subscription.price_monthly)}/month
            </p>
            {subscription.status === 'trialing' && subscription.trial_end && (
              <p className="text-blue-600 text-sm">
                Trial ends: {new Date(subscription.trial_end).toLocaleDateString()}
              </p>
            )}
          </div>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            subscription.status === 'active' ? 'bg-green-100 text-green-800' :
            subscription.status === 'trialing' ? 'bg-blue-100 text-blue-800' :
            subscription.status === 'past_due' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {subscription.status === 'trialing' ? 'Free Trial' : subscription.status}
          </span>
        </div>
        
        <div className="text-sm text-gray-600">
          <p>Next billing: {new Date(subscription.current_period_end).toLocaleDateString()}</p>
        </div>
      </div>

      {/* Usage */}
      {usage && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Usage</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            {[
              { label: 'Litters', used: usage.litters, limit: subscription.max_litters },
              { label: 'Puppies', used: usage.puppies, limit: subscription.max_puppies },
              { label: 'Customers', used: usage.customers, limit: subscription.max_customers },
              { label: 'Photos', used: usage.photos, limit: subscription.max_photos },
              { label: 'Documents', used: usage.documents, limit: subscription.max_documents },
            ].map((item) => {
              const percentage = getUsagePercentage(item.used, item.limit)
              const colorClass = getUsageColor(percentage)
              
              return (
                <div key={item.label} className="text-center">
                  <div className={`rounded-lg p-3 ${colorClass}`}>
                    <div className="text-2xl font-bold">
                      {item.used}
                      {item.limit && `/${item.limit}`}
                    </div>
                    <div className="text-sm font-medium">{item.label}</div>
                    {item.limit && (
                      <div className="text-xs mt-1">
                        {percentage.toFixed(0)}% used
                      </div>
                    )}
                    {item.limit === null && (
                      <div className="text-xs mt-1">Unlimited</div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Upgrade Options */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Upgrade Plan</h2>
        <div className="grid md:grid-cols-3 gap-4">
          {Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => {
            const isCurrent = subscription.plan_name.toLowerCase() === key
            
            return (
              <div key={key} className={`border rounded-lg p-4 ${isCurrent ? 'border-blue-500 bg-blue-50' : ''}`}>
                <h3 className="font-semibold mb-2">{plan.name}</h3>
                <div className="text-2xl font-bold mb-2">{formatPrice(plan.priceMonthly)}/mo</div>
                <div className="text-sm text-gray-600 mb-4">
                  or {formatPrice(plan.priceYearly)}/year (save 2 months)
                </div>
                
                {isCurrent ? (
                  <div className="text-blue-600 font-medium">Current Plan</div>
                ) : (
                  <div className="space-y-2">
                    <button
                      onClick={() => handleUpgrade(key, 'monthly')}
                      disabled={upgrading}
                      className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                    >
                      Upgrade Monthly
                    </button>
                    <button
                      onClick={() => handleUpgrade(key, 'yearly')}
                      disabled={upgrading}
                      className="w-full bg-green-600 text-white py-2 px-3 rounded text-sm hover:bg-green-700 disabled:opacity-50"
                    >
                      Upgrade Yearly
                    </button>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
