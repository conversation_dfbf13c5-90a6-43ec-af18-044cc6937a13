'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import { ImageUpload } from '@/components/ui/ImageUpload'
import { PriceInput } from '@/components/puppies/PriceInput'

interface PuppyFormData {
  name: string
  description: string
  birthDate: string
  color: string
  price: number | undefined
  litterId: string
  ownerId: string
  ownerType: 'breeder' | 'customer'
}

interface Litter {
  id: number
  litterCode: string
}

interface User {
  id: number
  firstName: string
  lastName: string
  userType: 'breeder' | 'customer'
}

interface Photo {
  id: number
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: Date
}

interface PuppyFormProps {
  initialData?: Partial<PuppyFormData>
  onSubmit: (data: PuppyFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
  submitLabel?: string
  puppyId?: number // For image upload when editing existing puppy
  preselectedLitterId?: string // For pre-selecting a litter
}

export function PuppyForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  submitLabel = 'Save Puppy',
  puppyId,
  preselectedLitterId
}: PuppyFormProps) {
  const [formData, setFormData] = useState<PuppyFormData>({
    name: '',
    description: '',
    birthDate: '',
    color: '',
    price: undefined,
    litterId: preselectedLitterId || '',
    ownerId: '',
    ownerType: 'breeder',
    ...initialData
  })

  const [litters, setLitters] = useState<Litter[]>([])
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loadingPhotos, setLoadingPhotos] = useState(false)

  // Load litters on component mount
  useEffect(() => {
    const loadLitters = async () => {
      try {
        const response = await fetch('/api/litters')
        if (response.ok) {
          const data = await response.json()
          setLitters(data.litters || [])
        }
      } catch (error) {
        console.error('Failed to load litters:', error)
      }
    }

    loadLitters()
  }, [])

  // Load photos for existing puppy
  useEffect(() => {
    if (puppyId) {
      const loadPhotos = async () => {
        try {
          setLoadingPhotos(true)
          const response = await fetch(`/api/photos?entityType=puppy&entityId=${puppyId}`)
          if (response.ok) {
            const data = await response.json()
            setPhotos(data.photos || [])
          }
        } catch (error) {
          console.error('Error loading photos:', error)
        } finally {
          setLoadingPhotos(false)
        }
      }

      loadPhotos()
    }
  }, [puppyId])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.name.trim()) {
      errors.name = 'Puppy name is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    await onSubmit(formData)
  }

  const ownerTypeOptions = [
    { value: 'breeder', label: 'Breeder' },
    { value: 'customer', label: 'Customer' },
  ]

  const litterOptions = [
    { value: '', label: 'No litter assigned' },
    ...litters.map(litter => ({ value: litter.id.toString(), label: litter.litterCode }))
  ]

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {preselectedLitterId && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-800">
                Adding Puppy to Litter
              </h4>
              <p className="text-sm text-blue-700 mt-1">
                This puppy will be added to the selected litter. You can change the litter assignment below if needed.
              </p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Puppy Name *"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={formErrors.name}
            required
            placeholder="e.g., Buddy, Luna, Max"
          />

          <Input
            label="Color"
            name="color"
            value={formData.color}
            onChange={handleChange}
            error={formErrors.color}
            placeholder="e.g., Golden, Black, Brown"
          />

          <Input
            label="Birth Date"
            name="birthDate"
            type="date"
            value={formData.birthDate}
            onChange={handleChange}
            error={formErrors.birthDate}
          />

          <Select
            label={preselectedLitterId ? "Litter (Pre-selected)" : "Litter"}
            name="litterId"
            value={formData.litterId}
            onChange={handleChange}
            options={litterOptions}
            error={formErrors.litterId}
          />

          <Select
            label="Owner Type"
            name="ownerType"
            value={formData.ownerType}
            onChange={handleChange}
            options={ownerTypeOptions}
            error={formErrors.ownerType}
          />

          <div className="md:col-span-1">
            <PriceInput
              label="Price"
              value={formData.price}
              onChange={(price) => setFormData(prev => ({ ...prev, price }))}
              error={formErrors.price}
              placeholder="Enter price (optional)"
            />
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            rows={4}
            value={formData.description}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Describe the puppy's personality, characteristics, health notes, etc."
          />
          {formErrors.description && (
            <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
          )}
        </div>

        {/* Image Upload Section */}
        {puppyId && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Puppy Photos</h3>
            {loadingPhotos ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <ImageUpload
                entityType="puppy"
                entityId={puppyId}
                photos={photos}
                onPhotosChange={setPhotos}
                maxFiles={15}
                disabled={loading}
              />
            )}
          </div>
        )}

        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            {submitLabel}
          </Button>
        </div>
      </form>
    </div>
  )
}
