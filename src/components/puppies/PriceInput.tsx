'use client'

import { useState } from 'react'

interface PriceInputProps {
  label: string
  value?: number
  onChange: (value: number | undefined) => void
  error?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
}

export function PriceInput({ 
  label, 
  value, 
  onChange, 
  error, 
  placeholder = "0.00",
  required = false,
  disabled = false 
}: PriceInputProps) {
  const [displayValue, setDisplayValue] = useState(
    value !== undefined ? value.toFixed(2) : ''
  )

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    setDisplayValue(inputValue)

    // Parse the value
    if (inputValue === '') {
      onChange(undefined)
      return
    }

    const numericValue = parseFloat(inputValue)
    if (!isNaN(numericValue) && numericValue >= 0) {
      onChange(numericValue)
    }
  }

  const handleBlur = () => {
    // Format the display value on blur
    if (value !== undefined) {
      setDisplayValue(value.toFixed(2))
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className="text-gray-500 sm:text-sm">$</span>
        </div>
        
        <input
          type="number"
          step="0.01"
          min="0"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            block w-full pl-7 pr-3 py-2 border rounded-md shadow-sm text-gray-900 placeholder-gray-500
            focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${error 
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300'
            }
          `}
        />
      </div>
      
      {value !== undefined && value > 0 && (
        <p className="text-xs text-gray-500">
          {formatCurrency(value)}
        </p>
      )}
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}
