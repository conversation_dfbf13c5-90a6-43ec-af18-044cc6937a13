'use client'

import { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from './Button'

interface Photo {
  id: number
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: Date
}

interface ImageUploadProps {
  entityType: 'litter' | 'puppy'
  entityId: number
  photos: Photo[]
  onPhotosChange: (photos: Photo[]) => void
  maxFiles?: number
  disabled?: boolean
}

export function ImageUpload({
  entityType,
  entityId,
  photos,
  onPhotosChange,
  maxFiles = 10,
  disabled = false
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = useCallback(async (files: FileList) => {
    if (disabled || uploading) return

    const fileArray = Array.from(files)
    const validFiles = fileArray.filter(file => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      const maxSize = 10 * 1024 * 1024 // 10MB
      return allowedTypes.includes(file.type) && file.size <= maxSize
    })

    if (validFiles.length === 0) {
      alert('Please select valid image files (JPEG, PNG, WebP) under 10MB')
      return
    }

    if (photos.length + validFiles.length > maxFiles) {
      alert(`You can only upload up to ${maxFiles} images`)
      return
    }

    setUploading(true)

    try {
      const uploadPromises = validFiles.map(async (file) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('entityType', entityType)
        formData.append('entityId', entityId.toString())

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Upload failed')
        }

        const result = await response.json()
        return result.photo
      })

      const uploadedPhotos = await Promise.all(uploadPromises)
      onPhotosChange([...photos, ...uploadedPhotos])
    } catch (error) {
      console.error('Upload error:', error)
      alert('Failed to upload some images. Please try again.')
    } finally {
      setUploading(false)
    }
  }, [entityType, entityId, photos, onPhotosChange, maxFiles, disabled, uploading])

  const handleDeletePhoto = async (photoId: number) => {
    if (disabled) return

    try {
      const response = await fetch(`/api/photos?id=${photoId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Delete failed')
      }

      onPhotosChange(photos.filter(photo => photo.id !== photoId))
    } catch (error) {
      console.error('Delete error:', error)
      alert('Failed to delete image. Please try again.')
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled && !uploading) {
      setDragOver(true)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (disabled || uploading) return

    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileSelect(files)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }

  const openFileDialog = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click()
    }
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragOver
            ? 'border-blue-400 bg-blue-50'
            : disabled
            ? 'border-gray-200 bg-gray-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled || uploading}
        />
        
        <div className="space-y-2">
          <svg
            className="w-12 h-12 text-gray-400 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          
          <div>
            <p className="text-gray-600">
              {uploading ? 'Uploading...' : 'Drag and drop images here, or'}
            </p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={openFileDialog}
              disabled={disabled || uploading}
              className="mt-2"
            >
              {uploading ? 'Uploading...' : 'Choose Files'}
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            JPEG, PNG, WebP up to 10MB each. Max {maxFiles} images.
          </p>
        </div>
      </div>

      {/* Photo Grid */}
      {photos.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group">
              <img
                src={photo.url}
                alt={photo.originalName}
                className="w-full h-32 object-cover rounded-lg border border-gray-200"
              />
              
              {!disabled && (
                <button
                  onClick={() => handleDeletePhoto(photo.id)}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                  title="Delete image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
              
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                <p className="truncate">{photo.originalName}</p>
                <p>{(photo.size / 1024 / 1024).toFixed(1)} MB</p>
              </div>
            </div>
          ))}
        </div>
      )}

      {photos.length === 0 && (
        <p className="text-gray-500 text-center py-4">No images uploaded yet</p>
      )}
    </div>
  )
}
