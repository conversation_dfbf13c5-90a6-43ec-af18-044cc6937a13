'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useUser } from '@/contexts/UserContext'
import { Button } from '@/components/ui/Button'

interface NavigationProps {
  userType?: 'customer' | 'breeder'
}

export function Navigation({ userType }: NavigationProps) {
  const { user, logout } = useUser()
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleLogout = async () => {
    await logout()
    router.push('/')
  }

  const getNavigationItems = () => {
    if (userType === 'breeder') {
      return [
        { name: 'Dashboard', href: '/breeder/dashboard' },
        { name: 'Litters', href: '/breeder/litters' },
        { name: 'Puppies', href: '/breeder/puppies' },
        { name: 'Customers', href: '/breeder/customers' },
        { name: '<PERSON>', href: '/breeder/profile' },
      ]
    } else if (userType === 'customer') {
      return [
        { name: 'Dashboard', href: '/customer/dashboard' },
        { name: '<PERSON><PERSON><PERSON> Puppies', href: '/customer/puppies' },
        { name: 'Profile', href: '/customer/profile' },
      ]
    }
    return []
  }

  const navigationItems = getNavigationItems()

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and brand */}
          <div className="flex items-center">
            <Link href={userType === 'breeder' ? '/breeder/dashboard' : '/customer/dashboard'} className="flex items-center">
              <Image
                src="/pawledger_logo.png"
                alt="PawLedger Logo"
                width={32}
                height={32}
                className="mr-2"
              />
              <span className="text-xl font-bold text-gray-900">PawLedger</span>
            </Link>
            
            {userType && (
              <span className={`ml-4 px-3 py-1 text-xs font-medium rounded-full ${
                userType === 'breeder' 
                  ? 'bg-blue-100 text-blue-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {userType === 'breeder' ? 'Breeder Admin' : 'Customer'}
              </span>
            )}
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </div>

          {/* User menu */}
          <div className="flex items-center space-x-4">
            {user && (
              <span className="hidden md:block text-sm text-gray-700">
                Welcome, {user.firstName}
              </span>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="hidden md:block"
            >
              Sign Out
            </Button>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              {user && (
                <div className="border-t border-gray-200 pt-4 pb-3">
                  <div className="px-3 py-2">
                    <div className="text-base font-medium text-gray-800">
                      {user.firstName} {user.lastName}
                    </div>
                    <div className="text-sm font-medium text-gray-500">
                      {user.email}
                    </div>
                  </div>
                  <div className="px-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLogout}
                      className="w-full"
                    >
                      Sign Out
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
