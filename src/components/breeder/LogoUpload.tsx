'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'

interface LogoUploadProps {
  currentLogoUrl?: string
  onLogoUpdate: (logoUrl: string | null) => void
  businessName: string
}

export function LogoUpload({ currentLogoUrl, onLogoUpdate, businessName }: LogoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentLogoUrl || null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setMessage({ type: 'error', text: 'Please select an image file' })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setMessage({ type: 'error', text: 'Image must be smaller than 5MB' })
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    // Upload file
    await uploadLogo(file)
  }

  const uploadLogo = async (file: File) => {
    try {
      setUploading(true)
      setMessage(null)

      const formData = new FormData()
      formData.append('file', file)
      formData.append('entityType', 'breeder')
      formData.append('entityId', '0') // We'll handle this in the API

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const data = await response.json()
      
      // Update the breeder with the new logo URL
      const updateResponse = await fetch('/api/breeders/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          logoUrl: data.url,
        }),
      })

      if (!updateResponse.ok) {
        throw new Error('Failed to update breeder logo')
      }

      setMessage({ type: 'success', text: 'Logo uploaded successfully!' })
      onLogoUpdate(data.url)
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000)

    } catch (error) {
      console.error('Logo upload error:', error)
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to upload logo' 
      })
      setPreviewUrl(currentLogoUrl || null)
    } finally {
      setUploading(false)
    }
  }

  const handleRemoveLogo = async () => {
    try {
      setUploading(true)
      setMessage(null)

      const response = await fetch('/api/breeders/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          logoUrl: null,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to remove logo')
      }

      setPreviewUrl(null)
      setMessage({ type: 'success', text: 'Logo removed successfully!' })
      onLogoUpdate(null)
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000)

    } catch (error) {
      console.error('Remove logo error:', error)
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to remove logo' 
      })
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Business Logo</h3>
        {previewUrl && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemoveLogo}
            disabled={uploading}
            className="text-red-600 hover:text-red-700"
          >
            Remove Logo
          </Button>
        )}
      </div>

      <div className="flex items-start space-x-6">
        {/* Logo Preview */}
        <div className="flex-shrink-0">
          <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
            {previewUrl ? (
              <div className="relative w-full h-full">
                <Image
                  src={previewUrl}
                  alt={`${businessName} logo`}
                  fill
                  className="object-contain rounded-lg"
                />
              </div>
            ) : (
              <div className="text-center">
                <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-xs text-gray-500">No logo</p>
              </div>
            )}
          </div>
        </div>

        {/* Upload Controls */}
        <div className="flex-1">
          <div className="space-y-3">
            <div>
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
                className="w-full sm:w-auto"
              >
                {uploading ? 'Uploading...' : previewUrl ? 'Change Logo' : 'Upload Logo'}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
            
            <div className="text-sm text-gray-500">
              <p>• Recommended size: 200x200 pixels or larger</p>
              <p>• Supported formats: JPG, PNG, GIF</p>
              <p>• Maximum file size: 5MB</p>
              <p>• Square images work best</p>
            </div>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {message && (
        <div className={`p-3 rounded-md ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}
    </div>
  )
}
