import { NextRequest, NextResponse } from 'next/server'
import { getBreederByCode, getLitterPuppies } from '@/lib/breeder-public'
import { db } from '@/lib/database'

// GET /api/public/breeders/[code]/litters/[litterId] - Get public litter details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ code: string; litterId: string }> }
) {
  try {
    const resolvedParams = await params
    const { code: breederCode, litterId } = resolvedParams

    if (!breederCode || !litterId) {
      return NextResponse.json(
        { error: 'Breeder code and litter ID are required' },
        { status: 400 }
      )
    }

    const litterIdNum = parseInt(litterId)
    if (isNaN(litterIdNum)) {
      return NextResponse.json(
        { error: 'Invalid litter ID' },
        { status: 400 }
      )
    }

    // Get breeder profile
    const breeder = await getBreederByCode(breederCode)
    
    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder not found' },
        { status: 404 }
      )
    }

    // Get litter details
    const litter = await db
      .selectFrom('litters')
      .leftJoin('puppies as mom', 'litters.mom_id', 'mom.id')
      .leftJoin('puppies as dad', 'litters.dad_id', 'dad.id')
      .select([
        'litters.id',
        'litters.litter_code',
        'litters.min_expected_size_lbs',
        'litters.max_expected_size_lbs',
        'litters.expected_birth_date',
        'litters.actual_birth_date',
        'litters.status',
        'litters.color',
        'litters.created_at',
        'mom.name as mom_name',
        'dad.name as dad_name'
      ])
      .where('litters.id', '=', litterIdNum)
      .where('litters.breeder_id', '=', breeder.id)
      .executeTakeFirst()

    if (!litter) {
      return NextResponse.json(
        { error: 'Litter not found' },
        { status: 404 }
      )
    }

    // Get litter photos
    const litterPhotos = await db
      .selectFrom('photos')
      .select(['id', 'filename', 'file_path'])
      .where('entity_type', '=', 'litter')
      .where('entity_id', '=', litterIdNum)
      .execute()

    // Get puppies in this litter
    const puppies = await getLitterPuppies(litterIdNum)

    const litterData = {
      id: litter.id,
      litterCode: litter.litter_code,
      minExpectedSizeLbs: litter.min_expected_size_lbs || undefined,
      maxExpectedSizeLbs: litter.max_expected_size_lbs || undefined,
      expectedBirthDate: litter.expected_birth_date || undefined,
      actualBirthDate: litter.actual_birth_date || undefined,
      status: litter.status as 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME',
      color: litter.color || undefined,
      momName: litter.mom_name || undefined,
      dadName: litter.dad_name || undefined,
      createdAt: litter.created_at,
      photos: litterPhotos.map(photo => ({
        id: photo.id,
        filename: photo.filename,
        url: photo.file_path
      })),
      puppies,
      stats: {
        totalPuppies: puppies.length,
        availablePuppies: puppies.filter(p => p.isAvailable).length,
        soldPuppies: puppies.filter(p => !p.isAvailable).length
      }
    }

    return NextResponse.json({
      breeder,
      litter: litterData
    })

  } catch (error) {
    console.error('Get public litter details error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
