import { NextRequest, NextResponse } from 'next/server'
import { getBreederByCode, getPublicLitters, getAvailablePuppies } from '@/lib/breeder-public'

// GET /api/public/breeders/[code] - Get public breeder profile
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ code: string }> }
) {
  try {
    const resolvedParams = await params
    const breederCode = resolvedParams.code

    if (!breederCode) {
      return NextResponse.json(
        { error: 'Breeder code is required' },
        { status: 400 }
      )
    }

    // Get breeder profile
    const breeder = await getBreederByCode(breederCode)
    
    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder not found' },
        { status: 404 }
      )
    }

    // Get litters and available puppies
    const [litters, availablePuppies] = await Promise.all([
      getPublicLitters(breeder.id),
      getAvailablePuppies(breeder.id)
    ])

    return NextResponse.json({
      breeder,
      litters,
      availablePuppies,
      stats: {
        totalLitters: litters.length,
        totalAvailablePuppies: availablePuppies.length,
        activeLitters: litters.filter(l => l.status === 'BORN' || l.status === 'READY_TO_GO_HOME').length
      }
    })

  } catch (error) {
    console.error('Get public breeder profile error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
