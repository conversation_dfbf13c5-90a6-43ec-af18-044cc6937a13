import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const entityType = searchParams.get('entityType') as 'litter' | 'puppy'
    const entityId = parseInt(searchParams.get('entityId') || '')

    if (!entityType || !['litter', 'puppy'].includes(entityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be "litter" or "puppy"' },
        { status: 400 }
      )
    }

    if (!entityId || isNaN(entityId)) {
      return NextResponse.json(
        { error: 'Invalid entity ID' },
        { status: 400 }
      )
    }

    // Get photos for the entity
    const photos = await db
      .selectFrom('photos')
      .select([
        'id',
        'filename',
        'original_name',
        'file_path',
        'file_size',
        'mime_type',
        'created_at'
      ])
      .where('entity_type', '=', entityType)
      .where('entity_id', '=', entityId)
      .orderBy('created_at', 'desc')
      .execute()

    const formattedPhotos = photos.map(photo => ({
      id: photo.id,
      filename: photo.filename,
      originalName: photo.original_name,
      url: photo.file_path,
      size: photo.file_size,
      mimeType: photo.mime_type,
      createdAt: photo.created_at,
    }))

    return NextResponse.json({
      success: true,
      photos: formattedPhotos
    })

  } catch (error) {
    console.error('Get photos error:', error)
    return NextResponse.json(
      { error: 'Failed to get photos' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const photoId = parseInt(searchParams.get('id') || '')

    if (!photoId || isNaN(photoId)) {
      return NextResponse.json(
        { error: 'Invalid photo ID' },
        { status: 400 }
      )
    }

    // Get photo details and verify ownership
    const photo = await db
      .selectFrom('photos')
      .leftJoin('litters', (join) => 
        join.on('photos.entity_type', '=', db.val('litter'))
            .on('photos.entity_id', '=', 'litters.id')
      )
      .leftJoin('puppies', (join) =>
        join.on('photos.entity_type', '=', db.val('puppy'))
            .on('photos.entity_id', '=', 'puppies.id')
      )
      .leftJoin('litters as puppy_litters', 'puppies.litter_id', 'puppy_litters.id')
      .select([
        'photos.id',
        'photos.filename',
        'photos.entity_type',
        'photos.entity_id',
        'litters.breeder_id as litter_breeder_id',
        'puppies.owner_id as puppy_owner_id',
        'puppies.owner_type as puppy_owner_type',
        'puppy_litters.breeder_id as puppy_litter_breeder_id'
      ])
      .where('photos.id', '=', photoId)
      .executeTakeFirst()

    if (!photo) {
      return NextResponse.json(
        { error: 'Photo not found' },
        { status: 404 }
      )
    }

    // Check if user can delete this photo
    let canDelete = false
    
    if (photo.entity_type === 'litter') {
      canDelete = photo.litter_breeder_id === session.userId
    } else if (photo.entity_type === 'puppy') {
      canDelete = 
        (photo.puppy_owner_id === session.userId && photo.puppy_owner_type === 'breeder') ||
        photo.puppy_litter_breeder_id === session.userId
    }

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only delete photos for entities you own.' },
        { status: 403 }
      )
    }

    // Delete from database
    await db
      .deleteFrom('photos')
      .where('id', '=', photoId)
      .execute()

    // Note: We could also delete from Google Cloud Storage here
    // but for now we'll keep the files for data integrity
    // await deleteImage(photo.filename)

    return NextResponse.json({
      success: true,
      message: 'Photo deleted successfully'
    })

  } catch (error) {
    console.error('Delete photo error:', error)
    return NextResponse.json(
      { error: 'Failed to delete photo' },
      { status: 500 }
    )
  }
}
