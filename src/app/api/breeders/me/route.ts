import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { getBreederByUserId, updateBreeder } from '@/lib/breeders'
import { getPublicLitters, getAvailablePuppies } from '@/lib/breeder-public'

export async function GET() {
  try {
    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    // Get statistics for dashboard
    const [litters, availablePuppies] = await Promise.all([
      getPublicLitters(breeder.id),
      getAvailablePuppies(breeder.id)
    ])

    const stats = {
      totalLitters: litters.length,
      totalAvailablePuppies: availablePuppies.length,
      activeLitters: litters.filter(l => l.status === 'BORN' || l.status === 'READY_TO_GO_HOME').length
    }

    return NextResponse.json({
      breeder: breeder,
      stats: stats
    })

  } catch (error) {
    console.error('Get breeder error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    const body = await request.json()

    // Validate the update data
    const updateData: any = {}

    if (body.businessName !== undefined) updateData.businessName = body.businessName
    if (body.businessPhone !== undefined) updateData.businessPhone = body.businessPhone
    if (body.businessStreetAddress !== undefined) updateData.businessStreetAddress = body.businessStreetAddress
    if (body.businessAptNumber !== undefined) updateData.businessAptNumber = body.businessAptNumber
    if (body.businessCity !== undefined) updateData.businessCity = body.businessCity
    if (body.businessState !== undefined) updateData.businessState = body.businessState
    if (body.businessZipCode !== undefined) updateData.businessZipCode = body.businessZipCode
    if (body.website !== undefined) updateData.website = body.website
    if (body.description !== undefined) updateData.description = body.description
    if (body.specialties !== undefined) updateData.specialties = body.specialties
    if (body.yearsExperience !== undefined) updateData.yearsExperience = body.yearsExperience
    if (body.logoUrl !== undefined) updateData.logoUrl = body.logoUrl

    const updatedBreeder = await updateBreeder(breeder.id, updateData)

    if (!updatedBreeder) {
      return NextResponse.json(
        { error: 'Failed to update breeder' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      breeder: updatedBreeder
    })

  } catch (error) {
    console.error('Update breeder error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
