import { NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { getBreederByUserId } from '@/lib/breeders'

export async function GET() {
  try {
    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const breeder = await getBreederByUserId(session.userId)
    
    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      breeder: breeder
    })

  } catch (error) {
    console.error('Get breeder error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
