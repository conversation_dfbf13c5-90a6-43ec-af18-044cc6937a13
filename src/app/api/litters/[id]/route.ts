import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { getLitterById, updateLitter, deleteLitter, UpdateLitterData } from '@/lib/litters'
import { getBreederByUserId } from '@/lib/breeders'

// GET /api/litters/[id] - Get a specific litter
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const litterId = parseInt(resolvedParams.id)
    if (isNaN(litterId)) {
      return NextResponse.json(
        { error: 'Invalid litter ID' },
        { status: 400 }
      )
    }

    const litter = await getLitterById(litterId)

    if (!litter) {
      return NextResponse.json(
        { error: 'Litter not found' },
        { status: 404 }
      )
    }

    // Get the breeder record to check ownership
    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    // Ensure the litter belongs to the authenticated breeder
    if (litter.breederId !== breeder.id) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only access your own litters.' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      litter
    })
  } catch (error) {
    console.error('Get litter error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/litters/[id] - Update a specific litter
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const litterId = parseInt(resolvedParams.id)
    if (isNaN(litterId)) {
      return NextResponse.json(
        { error: 'Invalid litter ID' },
        { status: 400 }
      )
    }

    // Check if litter exists and belongs to the breeder
    const existingLitter = await getLitterById(litterId)
    if (!existingLitter) {
      return NextResponse.json(
        { error: 'Litter not found' },
        { status: 404 }
      )
    }

    // Get the breeder record to check ownership
    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    if (existingLitter.breederId !== breeder.id) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only update your own litters.' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      litterCode,
      minExpectedSizeLbs,
      maxExpectedSizeLbs,
      expectedBirthDate,
      actualBirthDate,
      status,
      color,
      momId,
      dadId
    } = body

    // Validate status if provided
    if (status && !['NOT_BORN', 'BORN', 'READY_TO_GO_HOME'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required (NOT_BORN, BORN, READY_TO_GO_HOME)' },
        { status: 400 }
      )
    }

    const updateData: UpdateLitterData = {}
    
    if (litterCode !== undefined) updateData.litterCode = litterCode
    if (minExpectedSizeLbs !== undefined) updateData.minExpectedSizeLbs = minExpectedSizeLbs ? parseFloat(minExpectedSizeLbs) : undefined
    if (maxExpectedSizeLbs !== undefined) updateData.maxExpectedSizeLbs = maxExpectedSizeLbs ? parseFloat(maxExpectedSizeLbs) : undefined
    if (expectedBirthDate !== undefined) updateData.expectedBirthDate = expectedBirthDate ? new Date(expectedBirthDate) : undefined
    if (actualBirthDate !== undefined) updateData.actualBirthDate = actualBirthDate ? new Date(actualBirthDate) : undefined
    if (status !== undefined) updateData.status = status
    if (color !== undefined) updateData.color = color
    if (momId !== undefined) updateData.momId = momId ? parseInt(momId) : undefined
    if (dadId !== undefined) updateData.dadId = dadId ? parseInt(dadId) : undefined

    const updatedLitter = await updateLitter(litterId, updateData)

    if (!updatedLitter) {
      return NextResponse.json(
        { error: 'Failed to update litter' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      litter: updatedLitter
    })

  } catch (error) {
    console.error('Update litter error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/litters/[id] - Delete a specific litter
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const litterId = parseInt(resolvedParams.id)
    if (isNaN(litterId)) {
      return NextResponse.json(
        { error: 'Invalid litter ID' },
        { status: 400 }
      )
    }

    // Check if litter exists and belongs to the breeder
    const existingLitter = await getLitterById(litterId)
    if (!existingLitter) {
      return NextResponse.json(
        { error: 'Litter not found' },
        { status: 404 }
      )
    }

    // Get the breeder record to check ownership
    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    if (existingLitter.breederId !== breeder.id) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only delete your own litters.' },
        { status: 403 }
      )
    }

    const success = await deleteLitter(litterId, session.userId)

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete litter' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Litter deleted successfully'
    })

  } catch (error) {
    console.error('Delete litter error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
