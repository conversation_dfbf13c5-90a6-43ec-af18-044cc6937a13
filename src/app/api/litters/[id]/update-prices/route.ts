import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

// PUT /api/litters/[id]/update-prices - Update all puppy prices in a litter
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const litterId = parseInt(resolvedParams.id)
    
    if (isNaN(litterId)) {
      return NextResponse.json(
        { error: 'Invalid litter ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { price } = body

    // Validate price
    if (price === undefined || price === null) {
      return NextResponse.json(
        { error: 'Price is required' },
        { status: 400 }
      )
    }

    if (typeof price !== 'number' || price < 0) {
      return NextResponse.json(
        { error: 'Price must be a positive number' },
        { status: 400 }
      )
    }

    // Check if litter exists and belongs to this breeder
    const litter = await db
      .selectFrom('litters')
      .select(['id', 'breeder_id'])
      .where('id', '=', litterId)
      .executeTakeFirst()

    if (!litter) {
      return NextResponse.json(
        { error: 'Litter not found' },
        { status: 404 }
      )
    }

    if (litter.breeder_id !== session.userId) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only update your own litters.' },
        { status: 403 }
      )
    }

    // Update all puppies in the litter with the new price
    const result = await db
      .updateTable('puppies')
      .set({
        price: Math.round(price * 100), // Store as cents to avoid floating point issues
        updated_at: new Date()
      })
      .where('litter_id', '=', litterId)
      .executeTakeFirst()

    // Get updated puppy count
    const updatedCount = Number(result.numUpdatedRows || 0)

    // Get the updated puppies to return
    const updatedPuppies = await db
      .selectFrom('puppies')
      .select([
        'id',
        'name',
        'description',
        'birth_date',
        'color',
        'price',
        'litter_id',
        'owner_id',
        'owner_type',
        'created_at',
        'updated_at'
      ])
      .where('litter_id', '=', litterId)
      .execute()

    const formattedPuppies = updatedPuppies.map(puppy => ({
      id: puppy.id,
      name: puppy.name,
      description: puppy.description || '',
      birthDate: puppy.birth_date,
      color: puppy.color || '',
      price: puppy.price ? puppy.price / 100 : null, // Convert back from cents
      litterId: puppy.litter_id,
      ownerId: puppy.owner_id,
      ownerType: puppy.owner_type,
      createdAt: puppy.created_at,
      updatedAt: puppy.updated_at
    }))

    return NextResponse.json({
      success: true,
      message: `Updated prices for ${updatedCount} puppies`,
      updatedCount,
      price: price,
      puppies: formattedPuppies
    })

  } catch (error) {
    console.error('Update litter prices error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
