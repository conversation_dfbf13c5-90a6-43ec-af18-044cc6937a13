import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { createLitter, getLittersByBreederId, CreateLitterData } from '@/lib/litters'
import { getBreederByUserId } from '@/lib/breeders'
import { initializeDatabase } from '@/lib/database'

// GET /api/litters - Get litters for the authenticated breeder
export async function GET() {
  try {
    const session = await getSession()

    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    // Get the breeder record to get the breeder ID
    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    const litters = await getLittersByBreederId(breeder.id)

    return NextResponse.json({
      success: true,
      litters
    })
  } catch (error) {
    console.error('Get litters error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/litters - Create a new litter
export async function POST(request: NextRequest) {
  try {
    // Initialize database if needed
    await initializeDatabase()

    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      litterCode,
      minExpectedSizeLbs,
      maxExpectedSizeLbs,
      expectedBirthDate,
      actualBirthDate,
      status,
      color,
      momId,
      dadId
    } = body

    // Validate required fields
    if (!litterCode) {
      return NextResponse.json(
        { error: 'Litter code is required' },
        { status: 400 }
      )
    }

    if (!status || !['NOT_BORN', 'BORN', 'READY_TO_GO_HOME'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required (NOT_BORN, BORN, READY_TO_GO_HOME)' },
        { status: 400 }
      )
    }

    // Get the breeder record to get the breeder ID
    const breeder = await getBreederByUserId(session.userId)

    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    const litterData: CreateLitterData = {
      litterCode,
      minExpectedSizeLbs: minExpectedSizeLbs ? parseFloat(minExpectedSizeLbs) : undefined,
      maxExpectedSizeLbs: maxExpectedSizeLbs ? parseFloat(maxExpectedSizeLbs) : undefined,
      expectedBirthDate: expectedBirthDate ? new Date(expectedBirthDate) : undefined,
      actualBirthDate: actualBirthDate ? new Date(actualBirthDate) : undefined,
      status,
      breederId: breeder.id,
      color,
      momId: momId ? parseInt(momId) : undefined,
      dadId: dadId ? parseInt(dadId) : undefined,
    }

    const litter = await createLitter(litterData)

    return NextResponse.json({
      success: true,
      litter
    })

  } catch (error) {
    console.error('Create litter error:', error)
    
    if (error instanceof Error) {
      if (error.message === 'Litter code already exists') {
        return NextResponse.json(
          { error: 'Litter code already exists' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
