import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { getPuppyById, updatePuppy, deletePuppy, UpdatePuppyData } from '@/lib/puppies'

// GET /api/puppies/[id] - Get a specific puppy
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const puppyId = parseInt(resolvedParams.id)
    if (isNaN(puppyId)) {
      return NextResponse.json(
        { error: 'Invalid puppy ID' },
        { status: 400 }
      )
    }

    const puppy = await getPuppyById(puppyId)
    
    if (!puppy) {
      return NextResponse.json(
        { error: 'Puppy not found' },
        { status: 404 }
      )
    }

    // Check access permissions
    const hasAccess = 
      (puppy.ownerId === session.userId && puppy.ownerType === session.userType) ||
      session.userType === 'breeder' // Breeders can view all puppies

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only access puppies you own or manage.' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      puppy
    })
  } catch (error) {
    console.error('Get puppy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/puppies/[id] - Update a specific puppy
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const puppyId = parseInt(resolvedParams.id)
    if (isNaN(puppyId)) {
      return NextResponse.json(
        { error: 'Invalid puppy ID' },
        { status: 400 }
      )
    }

    // Check if puppy exists
    const existingPuppy = await getPuppyById(puppyId)
    if (!existingPuppy) {
      return NextResponse.json(
        { error: 'Puppy not found' },
        { status: 404 }
      )
    }

    // Check permissions - only breeders or the owner can update
    const canUpdate = 
      session.userType === 'breeder' ||
      (existingPuppy.ownerId === session.userId && existingPuppy.ownerType === session.userType)

    if (!canUpdate) {
      return NextResponse.json(
        { error: 'Unauthorized. You can only update puppies you own or manage.' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      birthDate,
      color,
      price,
      litterId,
      ownerId,
      ownerType
    } = body

    // Validate owner type if provided
    if (ownerType && !['breeder', 'customer'].includes(ownerType)) {
      return NextResponse.json(
        { error: 'Valid owner type is required (breeder, customer)' },
        { status: 400 }
      )
    }

    // Only breeders can change ownership
    if (session.userType !== 'breeder' && (ownerId !== undefined || ownerType !== undefined)) {
      return NextResponse.json(
        { error: 'Only breeders can change puppy ownership' },
        { status: 403 }
      )
    }

    const updateData: UpdatePuppyData = {}
    
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (birthDate !== undefined) updateData.birthDate = birthDate ? new Date(birthDate) : undefined
    if (color !== undefined) updateData.color = color
    if (price !== undefined) updateData.price = price ? parseFloat(price) : undefined
    if (litterId !== undefined) updateData.litterId = litterId ? parseInt(litterId) : undefined
    if (ownerId !== undefined) updateData.ownerId = ownerId ? parseInt(ownerId) : undefined
    if (ownerType !== undefined) updateData.ownerType = ownerType

    const updatedPuppy = await updatePuppy(puppyId, updateData)

    if (!updatedPuppy) {
      return NextResponse.json(
        { error: 'Failed to update puppy' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      puppy: updatedPuppy
    })

  } catch (error) {
    console.error('Update puppy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/puppies/[id] - Delete a specific puppy
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()

    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Only breeders can delete puppies.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const puppyId = parseInt(resolvedParams.id)
    if (isNaN(puppyId)) {
      return NextResponse.json(
        { error: 'Invalid puppy ID' },
        { status: 400 }
      )
    }

    // Check if puppy exists
    const existingPuppy = await getPuppyById(puppyId)
    if (!existingPuppy) {
      return NextResponse.json(
        { error: 'Puppy not found' },
        { status: 404 }
      )
    }

    const success = await deletePuppy(puppyId)

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete puppy' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Puppy deleted successfully'
    })

  } catch (error) {
    console.error('Delete puppy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
