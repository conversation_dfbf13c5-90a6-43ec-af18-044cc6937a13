import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { createPuppy, getPuppiesByOwner, CreatePuppyData } from '@/lib/puppies'
import { initializeDatabase } from '@/lib/database'

// GET /api/puppies - Get puppies for the authenticated user
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const litterId = searchParams.get('litterId')

    let puppies
    if (litterId) {
      // Get puppies by litter ID
      const { getPuppiesByLitterId } = await import('@/lib/puppies')
      puppies = await getPuppiesByLitterId(parseInt(litterId))
    } else {
      // Get puppies by owner
      puppies = await getPuppiesByOwner(session.userId, session.userType)
    }

    return NextResponse.json({
      success: true,
      puppies
    })
  } catch (error) {
    console.error('Get puppies error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/puppies - Create a new puppy
export async function POST(request: NextRequest) {
  try {
    // Initialize database if needed
    await initializeDatabase()

    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      birthDate,
      color,
      price,
      litterId,
      ownerId,
      ownerType
    } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Puppy name is required' },
        { status: 400 }
      )
    }

    // If no owner specified, default to the current user
    const finalOwnerId = ownerId || session.userId
    const finalOwnerType = ownerType || session.userType

    // Validate owner type
    if (finalOwnerType && !['breeder', 'customer'].includes(finalOwnerType)) {
      return NextResponse.json(
        { error: 'Valid owner type is required (breeder, customer)' },
        { status: 400 }
      )
    }

    // Only breeders can create puppies for other people
    if (session.userType !== 'breeder' && (finalOwnerId !== session.userId || finalOwnerType !== session.userType)) {
      return NextResponse.json(
        { error: 'Only breeders can assign puppies to other owners' },
        { status: 403 }
      )
    }

    const puppyData: CreatePuppyData = {
      name,
      description,
      birthDate: birthDate ? new Date(birthDate) : undefined,
      color,
      price: price ? parseFloat(price) : undefined,
      litterId: litterId ? parseInt(litterId) : undefined,
      ownerId: finalOwnerId,
      ownerType: finalOwnerType,
    }

    const puppy = await createPuppy(puppyData)

    return NextResponse.json({
      success: true,
      puppy
    })

  } catch (error) {
    console.error('Create puppy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
