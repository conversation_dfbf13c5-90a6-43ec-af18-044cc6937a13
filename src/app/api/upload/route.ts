import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { uploadImage, validateImageFile, fileToBuffer } from '@/lib/storage'
import { db } from '@/lib/database'
import { getBreederByUserId } from '@/lib/breeders'

export async function POST(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const entityType = formData.get('entityType') as 'litter' | 'puppy'
    const entityId = parseInt(formData.get('entityId') as string)

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!entityType || !['litter', 'puppy'].includes(entityType)) {
      return NextResponse.json(
        { error: 'Invalid entity type. Must be "litter" or "puppy"' },
        { status: 400 }
      )
    }

    // Entity ID is required for photos
    if (!entityId || isNaN(entityId)) {
      return NextResponse.json(
        { error: 'Invalid entity ID' },
        { status: 400 }
      )
    }

    // Validate file
    const validation = validateImageFile(file)
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }

    // Get the breeder record for the current user
    const breeder = await getBreederByUserId(session.userId)
    if (!breeder) {
      return NextResponse.json(
        { error: 'Breeder profile not found' },
        { status: 404 }
      )
    }

    // Verify entity exists and belongs to the breeder
    if (entityType === 'litter') {
      const litter = await db
        .selectFrom('litters')
        .select(['id', 'breeder_id'])
        .where('id', '=', entityId)
        .executeTakeFirst()

      if (!litter) {
        return NextResponse.json(
          { error: 'Litter not found' },
          { status: 404 }
        )
      }

      if (litter.breeder_id !== breeder.id) {
        return NextResponse.json(
          { error: 'Unauthorized. You can only upload images for your own litters.' },
          { status: 403 }
        )
      }
    } else if (entityType === 'puppy') {
      // For puppies, check if the breeder owns the puppy or the litter
      const puppy = await db
        .selectFrom('puppies')
        .leftJoin('litters', 'puppies.litter_id', 'litters.id')
        .select(['puppies.id', 'puppies.owner_id', 'puppies.owner_type', 'litters.breeder_id'])
        .where('puppies.id', '=', entityId)
        .executeTakeFirst()

      if (!puppy) {
        return NextResponse.json(
          { error: 'Puppy not found' },
          { status: 404 }
        )
      }

      const canUpload =
        (puppy.owner_id === session.userId && puppy.owner_type === 'breeder') ||
        puppy.breeder_id === breeder.id

      if (!canUpload) {
        return NextResponse.json(
          { error: 'Unauthorized. You can only upload images for puppies you own or breed.' },
          { status: 403 }
        )
      }
    }

    // Convert file to buffer and upload
    const fileBuffer = await fileToBuffer(file)
    const uploadResult = await uploadImage(
      fileBuffer,
      file.name,
      file.type,
      entityType,
      entityType === 'breeder' ? breeder.id : entityId
    )

    if (entityType === 'breeder') {
      // For breeder logos, just return the URL without saving to photos table
      return NextResponse.json({
        success: true,
        url: uploadResult.url,
        filename: uploadResult.filename,
      })
    }

    // Save photo record to database for litters and puppies
    const photoRecord = await db
      .insertInto('photos')
      .values({
        filename: uploadResult.filename,
        original_name: uploadResult.originalName,
        file_path: uploadResult.url,
        file_size: uploadResult.size,
        mime_type: uploadResult.mimeType,
        entity_type: entityType,
        entity_id: entityId,
      })
      .returning(['id', 'filename', 'original_name', 'file_path', 'created_at'])
      .executeTakeFirstOrThrow()

    return NextResponse.json({
      success: true,
      photo: {
        id: photoRecord.id,
        filename: photoRecord.filename,
        originalName: photoRecord.original_name,
        url: photoRecord.file_path,
        createdAt: photoRecord.created_at,
      }
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload image' },
      { status: 500 }
    )
  }
}
