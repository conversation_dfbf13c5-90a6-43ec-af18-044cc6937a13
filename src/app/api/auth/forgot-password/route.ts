import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import crypto from 'crypto'

// POST /api/auth/forgot-password - Send password reset email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    // Validate email
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await db
      .selectFrom('users')
      .select(['id', 'email', 'first_name', 'last_name'])
      .where('email', '=', email.toLowerCase())
      .executeTakeFirst()

    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user) {
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now

      // Store reset token in database
      await db
        .updateTable('users')
        .set({
          reset_token: resetToken,
          reset_token_expiry: resetTokenExpiry,
          updated_at: new Date()
        })
        .where('id', '=', user.id)
        .execute()

      // In a real application, you would send an email here
      // For now, we'll log the reset link to the console
      const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/reset-password?token=${resetToken}`
      
      console.log('=== PASSWORD RESET EMAIL ===')
      console.log(`To: ${user.email}`)
      console.log(`Name: ${user.first_name} ${user.last_name}`)
      console.log(`Reset URL: ${resetUrl}`)
      console.log('============================')

      // TODO: Replace with actual email sending service
      // Example with a service like SendGrid, Nodemailer, etc.
      /*
      await sendEmail({
        to: user.email,
        subject: 'Reset Your PawLedger Password',
        html: `
          <h2>Password Reset Request</h2>
          <p>Hello ${user.first_name},</p>
          <p>You requested a password reset for your PawLedger account.</p>
          <p>Click the link below to reset your password:</p>
          <a href="${resetUrl}" style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <p>Best regards,<br>The PawLedger Team</p>
        `
      })
      */
    }

    // Always return success response
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, we have sent a password reset link.'
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
