import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { sendEmail, generatePasswordResetEmail } from '@/lib/email'
import crypto from 'crypto'

// POST /api/auth/forgot-password - Send password reset email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    // Validate email
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await db
      .selectFrom('users')
      .select(['id', 'email', 'first_name', 'last_name'])
      .where('email', '=', email.toLowerCase())
      .executeTakeFirst()

    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user) {
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex')
      const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now

      // Store reset token in database
      await db
        .updateTable('users')
        .set({
          reset_token: resetToken,
          reset_token_expiry: resetTokenExpiry,
          updated_at: new Date()
        })
        .where('id', '=', user.id)
        .execute()

      // Generate reset URL
      const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`

      // Generate email content
      const userName = `${user.first_name} ${user.last_name}`
      const emailContent = generatePasswordResetEmail(userName, resetUrl)

      // Send password reset email
      try {
        await sendEmail({
          to: user.email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        })
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError)
        // Don't fail the request if email fails - for better user experience
        // In production, you might want to log this to an error tracking service
      }
    }

    // Always return success response
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, we have sent a password reset link.'
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
