import { NextRequest, NextResponse } from 'next/server'
import { createUser, CreateUserData } from '@/lib/auth'
import { createSession } from '@/lib/session'
import { initializeDatabase } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // Initialize database if needed
    await initializeDatabase()

    const body = await request.json()
    const {
      email,
      password,
      userType,
      firstName,
      lastName,
      phone,
      streetAddress,
      aptNumber,
      city,
      state,
      zipCode,
      businessName,
      businessPhone,
      businessStreetAddress,
      businessAptNumber,
      businessCity,
      businessState,
      businessZipCode
    } = body

    // Validate required fields
    if (!email || !password || !userType || !firstName || !lastName || !streetAddress || !city || !state || !zipCode) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate user type
    if (userType !== 'customer' && userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Invalid user type' },
        { status: 400 }
      )
    }

    // For breeders, business name is required
    if (userType === 'breeder' && !businessName) {
      return NextResponse.json(
        { error: 'Business name is required for breeders' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    const userData: CreateUserData = {
      email,
      password,
      userType,
      firstName,
      lastName,
      phone: phone || undefined,
      streetAddress,
      aptNumber: aptNumber || undefined,
      city,
      state,
      zipCode,
      businessName: userType === 'breeder' ? businessName : undefined,
      businessPhone: userType === 'breeder' ? businessPhone : undefined,
      businessStreetAddress: userType === 'breeder' ? businessStreetAddress : undefined,
      businessAptNumber: userType === 'breeder' ? businessAptNumber : undefined,
      businessCity: userType === 'breeder' ? businessCity : undefined,
      businessState: userType === 'breeder' ? businessState : undefined,
      businessZipCode: userType === 'breeder' ? businessZipCode : undefined,
    }

    const user = await createUser(userData)
    await createSession(user)

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        userType: user.userType,
        firstName: user.firstName,
        lastName: user.lastName,
      }
    })

  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof Error) {
      if (error.message === 'Email already exists') {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
