import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

// GET /api/customers/[id] - Get a specific customer relationship
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const relationshipId = parseInt(resolvedParams.id)
    
    if (isNaN(relationshipId)) {
      return NextResponse.json(
        { error: 'Invalid relationship ID' },
        { status: 400 }
      )
    }

    const customer = await db
      .selectFrom('customer_breeder_relationships as cbr')
      .innerJoin('users as customers', 'cbr.customer_id', 'customers.id')
      .select([
        'cbr.id as relationshipId',
        'cbr.relationship_type',
        'cbr.status',
        'cbr.notes',
        'cbr.created_at as relationshipCreatedAt',
        'cbr.updated_at as relationshipUpdatedAt',
        'customers.id as customerId',
        'customers.first_name',
        'customers.last_name',
        'customers.email',
        'customers.phone',
        'customers.street_address',
        'customers.apt_number',
        'customers.city',
        'customers.state',
        'customers.zip_code',
        'customers.created_at as customerCreatedAt'
      ])
      .where('cbr.id', '=', relationshipId)
      .where('cbr.breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer relationship not found' },
        { status: 404 }
      )
    }

    const formattedCustomer = {
      relationshipId: customer.relationshipId,
      customerId: customer.customerId,
      firstName: customer.first_name,
      lastName: customer.last_name,
      email: customer.email,
      phone: customer.phone || '',
      address: {
        street: customer.street_address || '',
        aptNumber: customer.apt_number || '',
        city: customer.city || '',
        state: customer.state || '',
        zipCode: customer.zip_code || ''
      },
      relationshipType: customer.relationship_type,
      status: customer.status,
      notes: customer.notes || '',
      customerCreatedAt: customer.customerCreatedAt,
      relationshipCreatedAt: customer.relationshipCreatedAt,
      relationshipUpdatedAt: customer.relationshipUpdatedAt
    }

    return NextResponse.json({
      success: true,
      customer: formattedCustomer
    })

  } catch (error) {
    console.error('Get customer error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/customers/[id] - Update a customer relationship
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const relationshipId = parseInt(resolvedParams.id)
    
    if (isNaN(relationshipId)) {
      return NextResponse.json(
        { error: 'Invalid relationship ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      relationshipType,
      status,
      notes,
      // Customer details
      firstName,
      lastName,
      phone,
      streetAddress,
      aptNumber,
      city,
      state,
      zipCode
    } = body

    // Validate relationship type
    if (relationshipType && !['owner', 'lead'].includes(relationshipType)) {
      return NextResponse.json(
        { error: 'Valid relationship type (owner or lead) is required' },
        { status: 400 }
      )
    }

    // Validate status
    if (status && !['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status (active or inactive) is required' },
        { status: 400 }
      )
    }

    // Validate customer details if provided
    if (firstName !== undefined && !firstName?.trim()) {
      return NextResponse.json(
        { error: 'First name cannot be empty' },
        { status: 400 }
      )
    }

    if (lastName !== undefined && !lastName?.trim()) {
      return NextResponse.json(
        { error: 'Last name cannot be empty' },
        { status: 400 }
      )
    }

    if (phone !== undefined && phone?.trim() && !/^\+?[\d\s\-\(\)]+$/.test(phone.trim())) {
      return NextResponse.json(
        { error: 'Please enter a valid phone number' },
        { status: 400 }
      )
    }

    if (zipCode !== undefined && zipCode?.trim() && !/^\d{5}(-\d{4})?$/.test(zipCode.trim())) {
      return NextResponse.json(
        { error: 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789)' },
        { status: 400 }
      )
    }

    // Check if relationship exists and belongs to this breeder, and get customer ID
    const existingRelationship = await db
      .selectFrom('customer_breeder_relationships')
      .select(['id', 'customer_id'])
      .where('id', '=', relationshipId)
      .where('breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!existingRelationship) {
      return NextResponse.json(
        { error: 'Customer relationship not found' },
        { status: 404 }
      )
    }

    // Update the relationship
    const updateData: Record<string, unknown> = {
      updated_at: new Date()
    }

    if (relationshipType) {
      updateData.relationship_type = relationshipType
    }

    if (status) {
      updateData.status = status
    }

    if (notes !== undefined) {
      updateData.notes = notes?.trim() || null
    }

    // Update customer information if provided
    const hasCustomerUpdates = firstName !== undefined || lastName !== undefined ||
                              phone !== undefined || streetAddress !== undefined ||
                              aptNumber !== undefined || city !== undefined ||
                              state !== undefined || zipCode !== undefined

    if (hasCustomerUpdates) {
      const customerUpdateData: Record<string, unknown> = {
        updated_at: new Date()
      }

      if (firstName !== undefined) {
        customerUpdateData.first_name = firstName.trim()
      }

      if (lastName !== undefined) {
        customerUpdateData.last_name = lastName.trim()
      }

      if (phone !== undefined) {
        customerUpdateData.phone = phone?.trim() || null
      }

      if (streetAddress !== undefined) {
        customerUpdateData.street_address = streetAddress?.trim() || null
      }

      if (aptNumber !== undefined) {
        customerUpdateData.apt_number = aptNumber?.trim() || null
      }

      if (city !== undefined) {
        customerUpdateData.city = city?.trim() || null
      }

      if (state !== undefined) {
        customerUpdateData.state = state?.trim() || null
      }

      if (zipCode !== undefined) {
        customerUpdateData.zip_code = zipCode?.trim() || null
      }

      // Update physical_address for backward compatibility
      if (streetAddress !== undefined || aptNumber !== undefined ||
          city !== undefined || state !== undefined || zipCode !== undefined) {
        const addressParts = [
          streetAddress?.trim() || '',
          aptNumber?.trim() ? `Apt ${aptNumber.trim()}` : '',
          city?.trim() || '',
          state?.trim() || '',
          zipCode?.trim() || ''
        ].filter(Boolean)
        customerUpdateData.physical_address = addressParts.length > 0 ?
          addressParts.join(', ') : 'Address not provided'
      }

      // Update customer information
      await db
        .updateTable('users')
        .set(customerUpdateData)
        .where('id', '=', existingRelationship.customer_id)
        .execute()
    }

    const updatedRelationship = await db
      .updateTable('customer_breeder_relationships')
      .set(updateData)
      .where('id', '=', relationshipId)
      .where('breeder_id', '=', session.userId)
      .returning([
        'id',
        'relationship_type',
        'status',
        'notes',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return NextResponse.json({
      success: true,
      message: 'Customer relationship updated successfully',
      relationship: {
        relationshipId: updatedRelationship.id,
        relationshipType: updatedRelationship.relationship_type,
        status: updatedRelationship.status,
        notes: updatedRelationship.notes,
        updatedAt: updatedRelationship.updated_at
      }
    })

  } catch (error) {
    console.error('Update customer error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/customers/[id] - Remove a customer relationship
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const relationshipId = parseInt(resolvedParams.id)
    
    if (isNaN(relationshipId)) {
      return NextResponse.json(
        { error: 'Invalid relationship ID' },
        { status: 400 }
      )
    }

    // Check if relationship exists and belongs to this breeder
    const existingRelationship = await db
      .selectFrom('customer_breeder_relationships')
      .select(['id'])
      .where('id', '=', relationshipId)
      .where('breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!existingRelationship) {
      return NextResponse.json(
        { error: 'Customer relationship not found' },
        { status: 404 }
      )
    }

    // Delete the relationship
    await db
      .deleteFrom('customer_breeder_relationships')
      .where('id', '=', relationshipId)
      .where('breeder_id', '=', session.userId)
      .execute()

    return NextResponse.json({
      success: true,
      message: 'Customer relationship removed successfully'
    })

  } catch (error) {
    console.error('Delete customer error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
