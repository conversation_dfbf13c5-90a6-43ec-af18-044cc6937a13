import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'
import bcrypt from 'bcryptjs'

export async function GET(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const relationshipType = searchParams.get('relationshipType') || ''
    const status = searchParams.get('status') || 'active'
    const sortBy = searchParams.get('sortBy') || 'created_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build the query
    let query = db
      .selectFrom('customer_breeder_relationships as cbr')
      .innerJoin('users as customers', 'cbr.customer_id', 'customers.id')
      .select([
        'cbr.id as relationshipId',
        'cbr.relationship_type',
        'cbr.status',
        'cbr.notes',
        'cbr.created_at as relationshipCreatedAt',
        'cbr.updated_at as relationshipUpdatedAt',
        'customers.id as customerId',
        'customers.first_name',
        'customers.last_name',
        'customers.email',
        'customers.phone',
        'customers.street_address',
        'customers.apt_number',
        'customers.city',
        'customers.state',
        'customers.zip_code',
        'customers.created_at as customerCreatedAt'
      ])
      .where('cbr.breeder_id', '=', session.userId)

    // Apply filters
    if (status && (status === 'active' || status === 'inactive')) {
      query = query.where('cbr.status', '=', status as 'active' | 'inactive')
    }

    if (relationshipType && (relationshipType === 'owner' || relationshipType === 'lead')) {
      query = query.where('cbr.relationship_type', '=', relationshipType as 'owner' | 'lead')
    }

    if (search) {
      query = query.where((eb) =>
        eb.or([
          eb('customers.first_name', 'ilike', `%${search}%`),
          eb('customers.last_name', 'ilike', `%${search}%`),
          eb('customers.email', 'ilike', `%${search}%`),
          eb('customers.phone', 'ilike', `%${search}%`),
          eb('customers.city', 'ilike', `%${search}%`),
        ])
      )
    }

    // Apply sorting
    const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc'

    if (sortBy === 'first_name') {
      query = query.orderBy('customers.first_name', sortDirection)
    } else if (sortBy === 'last_name') {
      query = query.orderBy('customers.last_name', sortDirection)
    } else if (sortBy === 'email') {
      query = query.orderBy('customers.email', sortDirection)
    } else if (sortBy === 'relationship_type') {
      query = query.orderBy('cbr.relationship_type', sortDirection)
    } else if (sortBy === 'status') {
      query = query.orderBy('cbr.status', sortDirection)
    } else if (sortBy === 'customerCreatedAt') {
      query = query.orderBy('customers.created_at', sortDirection)
    } else if (sortBy === 'relationshipCreatedAt') {
      query = query.orderBy('cbr.created_at', sortDirection)
    } else {
      // Default sort by relationship created date
      query = query.orderBy('cbr.created_at', sortDirection)
    }

    // Get total count for pagination
    let countQuery = db
      .selectFrom('customer_breeder_relationships as cbr')
      .innerJoin('users as customers', 'cbr.customer_id', 'customers.id')
      .where('cbr.breeder_id', '=', session.userId)

    if (status && (status === 'active' || status === 'inactive')) {
      countQuery = countQuery.where('cbr.status', '=', status as 'active' | 'inactive')
    }
    if (relationshipType && (relationshipType === 'owner' || relationshipType === 'lead')) {
      countQuery = countQuery.where('cbr.relationship_type', '=', relationshipType as 'owner' | 'lead')
    }
    if (search) {
      countQuery = countQuery.where((eb) =>
        eb.or([
          eb('customers.first_name', 'ilike', `%${search}%`),
          eb('customers.last_name', 'ilike', `%${search}%`),
          eb('customers.email', 'ilike', `%${search}%`),
          eb('customers.phone', 'ilike', `%${search}%`),
          eb('customers.city', 'ilike', `%${search}%`),
        ])
      )
    }

    const [customers, totalResult] = await Promise.all([
      query.limit(limit).offset(offset).execute(),
      countQuery.select(db.fn.countAll().as('count')).executeTakeFirst()
    ])

    const total = Number(totalResult?.count || 0)
    const totalPages = Math.ceil(total / limit)

    const formattedCustomers = customers.map(customer => ({
      relationshipId: customer.relationshipId,
      customerId: customer.customerId,
      firstName: customer.first_name,
      lastName: customer.last_name,
      email: customer.email,
      phone: customer.phone || '',
      address: {
        street: customer.street_address || '',
        aptNumber: customer.apt_number || '',
        city: customer.city || '',
        state: customer.state || '',
        zipCode: customer.zip_code || ''
      },
      relationshipType: customer.relationship_type,
      status: customer.status,
      notes: customer.notes || '',
      customerCreatedAt: customer.customerCreatedAt,
      relationshipCreatedAt: customer.relationshipCreatedAt,
      relationshipUpdatedAt: customer.relationshipUpdatedAt
    }))

    return NextResponse.json({
      success: true,
      customers: formattedCustomers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Get customers error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      email,
      firstName,
      lastName,
      phone,
      streetAddress,
      aptNumber,
      city,
      state,
      zipCode,
      relationshipType,
      notes
    } = body

    // Validate required fields
    if (!email?.trim() || !firstName?.trim() || !lastName?.trim()) {
      return NextResponse.json(
        { error: 'Email, first name, and last name are required' },
        { status: 400 }
      )
    }

    if (!relationshipType || !['owner', 'lead'].includes(relationshipType)) {
      return NextResponse.json(
        { error: 'Valid relationship type (owner or lead) is required' },
        { status: 400 }
      )
    }

    // Check if customer already exists
    let customer = await db
      .selectFrom('users')
      .select(['id', 'email', 'user_type'])
      .where('email', '=', email.trim().toLowerCase())
      .executeTakeFirst()

    if (customer) {
      // Customer exists, check if relationship already exists
      const existingRelationship = await db
        .selectFrom('customer_breeder_relationships')
        .select(['id'])
        .where('customer_id', '=', customer.id)
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst()

      if (existingRelationship) {
        return NextResponse.json(
          { error: 'This customer is already associated with your account' },
          { status: 400 }
        )
      }
    } else {
      // Create new customer
      const tempPassword = Math.random().toString(36).slice(-8) // Generate temporary password
      const hashedPassword = await bcrypt.hash(tempPassword, 10)

      // Build physical address from components for backward compatibility
      // Address parts are now stored in separate fields

      customer = await db
        .insertInto('users')
        .values({
          email: email.trim().toLowerCase(),
          password_hash: hashedPassword, // Temporary password - customer will reset on first login
          user_type: 'customer',
          first_name: firstName.trim(),
          last_name: lastName.trim(),
          phone: phone?.trim() || null,
          street_address: streetAddress?.trim() || null,
          apt_number: aptNumber?.trim() || null,
          city: city?.trim() || null,
          state: state?.trim() || null,
          zip_code: zipCode?.trim() || null,
        })
        .returning(['id', 'email', 'user_type'])
        .executeTakeFirstOrThrow()
    }

    // Create the relationship
    const relationship = await db
      .insertInto('customer_breeder_relationships')
      .values({
        customer_id: customer.id,
        breeder_id: session.userId,
        relationship_type: relationshipType,
        status: 'active',
        notes: notes?.trim() || null,
      })
      .returning([
        'id',
        'relationship_type',
        'status',
        'notes',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return NextResponse.json({
      success: true,
      message: 'Customer added successfully',
      customer: {
        relationshipId: relationship.id,
        customerId: customer.id,
        email: customer.email,
        relationshipType: relationship.relationship_type,
        status: relationship.status,
        notes: relationship.notes,
        createdAt: relationship.created_at
      }
    })

  } catch (error) {
    console.error('Add customer error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
