import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { db } from '@/lib/database'
import Stripe from 'stripe'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 })
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const userId = parseInt(session.metadata?.userId || '0')
  const planType = session.metadata?.planType
  const interval = session.metadata?.interval

  if (!userId || !planType || !interval) {
    console.error('Missing metadata in checkout session:', session.metadata)
    return
  }

  // Get the subscription from Stripe
  const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
  
  // Get the plan from our database
  const plan = await db
    .selectFrom('subscription_plans')
    .selectAll()
    .where('name', '=', planType)
    .executeTakeFirst()

  if (!plan) {
    console.error('Plan not found:', planType)
    return
  }

  // Create subscription record in our database
  await db
    .insertInto('subscriptions')
    .values({
      user_id: userId,
      plan_id: plan.id,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      status: subscription.status as any,
      current_period_start: new Date(subscription.current_period_start * 1000),
      current_period_end: new Date(subscription.current_period_end * 1000),
      trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
    })
    .execute()

  console.log(`Subscription created for user ${userId}:`, subscription.id)
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  // This is usually handled by checkout.session.completed
  console.log('Subscription created:', subscription.id)
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  await db
    .updateTable('subscriptions')
    .set({
      status: subscription.status as any,
      current_period_start: new Date(subscription.current_period_start * 1000),
      current_period_end: new Date(subscription.current_period_end * 1000),
      trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
      updated_at: new Date(),
    })
    .where('stripe_subscription_id', '=', subscription.id)
    .execute()

  console.log('Subscription updated:', subscription.id)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  await db
    .updateTable('subscriptions')
    .set({
      status: 'canceled',
      canceled_at: new Date(),
      updated_at: new Date(),
    })
    .where('stripe_subscription_id', '=', subscription.id)
    .execute()

  console.log('Subscription deleted:', subscription.id)
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  // Update subscription status to active if it was past_due
  if (invoice.subscription) {
    await db
      .updateTable('subscriptions')
      .set({
        status: 'active',
        updated_at: new Date(),
      })
      .where('stripe_subscription_id', '=', invoice.subscription as string)
      .execute()
  }

  console.log('Invoice payment succeeded:', invoice.id)
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  // Update subscription status to past_due
  if (invoice.subscription) {
    await db
      .updateTable('subscriptions')
      .set({
        status: 'past_due',
        updated_at: new Date(),
      })
      .where('stripe_subscription_id', '=', invoice.subscription as string)
      .execute()
  }

  console.log('Invoice payment failed:', invoice.id)
}
