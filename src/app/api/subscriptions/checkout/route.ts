import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth'
import Stripe from 'stripe'

// Initialize Stripe here to avoid import issues
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

type PlanType = 'starter' | 'professional' | 'premium'

export async function POST(request: NextRequest) {
  try {
    const session = await verifySession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { planType, interval } = await request.json()

    if (!planType || !interval) {
      return NextResponse.json(
        { error: 'Plan type and interval are required' },
        { status: 400 }
      )
    }

    if (!['starter', 'professional', 'premium'].includes(planType)) {
      return NextResponse.json(
        { error: 'Invalid plan type' },
        { status: 400 }
      )
    }

    if (!['monthly', 'yearly'].includes(interval)) {
      return NextResponse.json(
        { error: 'Invalid interval' },
        { status: 400 }
      )
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3003'

    // Get price ID for the plan (placeholder for now)
    const priceId = `price_${planType}_${interval}`

    // Create Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${baseUrl}/breeder/settings?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${baseUrl}/breeder/settings?canceled=true`,
      trial_period_days: 14, // 14-day free trial
      metadata: {
        userId: session.userId.toString(),
        planType,
        interval,
      }
    })

    return NextResponse.json({ url: checkoutSession.url })
  } catch (error) {
    console.error('Checkout session creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
