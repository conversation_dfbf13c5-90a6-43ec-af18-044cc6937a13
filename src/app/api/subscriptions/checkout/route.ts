import { NextRequest, NextResponse } from 'next/server'
import { createCheckoutSession, PlanType } from '@/lib/stripe'
import { verifySession } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await verifySession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { planType, interval } = await request.json()

    if (!planType || !interval) {
      return NextResponse.json(
        { error: 'Plan type and interval are required' },
        { status: 400 }
      )
    }

    if (!['starter', 'professional', 'premium'].includes(planType)) {
      return NextResponse.json(
        { error: 'Invalid plan type' },
        { status: 400 }
      )
    }

    if (!['monthly', 'yearly'].includes(interval)) {
      return NextResponse.json(
        { error: 'Invalid interval' },
        { status: 400 }
      )
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const checkoutSession = await createCheckoutSession({
      userId: session.userId,
      email: session.email,
      planType: planType as PlanType,
      interval: interval as 'monthly' | 'yearly',
      successUrl: `${baseUrl}/breeder/settings?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${baseUrl}/breeder/settings?canceled=true`,
    })

    return NextResponse.json({ url: checkoutSession.url })
  } catch (error) {
    console.error('Checkout session creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
