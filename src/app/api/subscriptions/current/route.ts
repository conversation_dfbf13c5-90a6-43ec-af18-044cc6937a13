import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await getSession()
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current subscription with plan details
    const subscription = await db
      .selectFrom('subscriptions')
      .innerJoin('subscription_plans', 'subscriptions.plan_id', 'subscription_plans.id')
      .select([
        'subscriptions.id',
        'subscriptions.status',
        'subscriptions.current_period_start',
        'subscriptions.current_period_end',
        'subscriptions.trial_end',
        'subscriptions.canceled_at',
        'subscription_plans.name as plan_name',
        'subscription_plans.price_monthly',
        'subscription_plans.price_yearly',
        'subscription_plans.max_litters',
        'subscription_plans.max_puppies',
        'subscription_plans.max_customers',
        'subscription_plans.max_photos',
        'subscription_plans.max_documents',
        'subscription_plans.features',
      ])
      .where('subscriptions.user_id', '=', session.userId)
      .where('subscriptions.status', 'in', ['active', 'trialing', 'past_due'])
      .orderBy('subscriptions.created_at', 'desc')
      .executeTakeFirst()

    if (!subscription) {
      return NextResponse.json({ subscription: null })
    }

    // Get current usage
    const [litterCount, puppyCount, customerCount, photoCount, documentCount] = await Promise.all([
      db.selectFrom('litters')
        .select(db.fn.countAll().as('count'))
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst(),
      db.selectFrom('puppies')
        .select(db.fn.countAll().as('count'))
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst(),
      db.selectFrom('customer_breeder_relationships')
        .select(db.fn.countAll().as('count'))
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst(),
      db.selectFrom('photos')
        .select(db.fn.countAll().as('count'))
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst(),
      db.selectFrom('documents')
        .select(db.fn.countAll().as('count'))
        .where('breeder_id', '=', session.userId)
        .executeTakeFirst(),
    ])

    const usage = {
      litters: Number(litterCount?.count || 0),
      puppies: Number(puppyCount?.count || 0),
      customers: Number(customerCount?.count || 0),
      photos: Number(photoCount?.count || 0),
      documents: Number(documentCount?.count || 0),
    }

    return NextResponse.json({
      subscription: {
        ...subscription,
        features: JSON.parse(subscription.features as string),
      },
      usage,
    })
  } catch (error) {
    console.error('Get subscription error:', error)
    return NextResponse.json(
      { error: 'Failed to get subscription' },
      { status: 500 }
    )
  }
}
