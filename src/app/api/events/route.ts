import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const eventType = searchParams.get('eventType') || ''
    const status = searchParams.get('status') || 'scheduled'
    const limit = parseInt(searchParams.get('limit') || '50')

    // Build the query
    let query = db
      .selectFrom('events')
      .select([
        'id',
        'title',
        'description',
        'event_date',
        'event_time',
        'event_type',
        'location',
        'all_day',
        'reminder_minutes',
        'status',
        'created_at',
        'updated_at'
      ])
      .where('breeder_id', '=', session.userId)

    // Apply filters
    if (status && (status === 'scheduled' || status === 'completed' || status === 'cancelled')) {
      query = query.where('status', '=', status as 'scheduled' | 'completed' | 'cancelled')
    }

    if (eventType && ['appointment', 'breeding', 'health_check', 'show', 'training', 'other'].includes(eventType)) {
      query = query.where('event_type', '=', eventType as 'appointment' | 'breeding' | 'health_check' | 'show' | 'training' | 'other')
    }

    if (startDate) {
      query = query.where('event_date', '>=', new Date(startDate))
    }

    if (endDate) {
      query = query.where('event_date', '<=', new Date(endDate))
    }

    // Order by date and time
    query = query.orderBy('event_date', 'asc').orderBy('event_time', 'asc')

    if (limit > 0) {
      query = query.limit(limit)
    }

    const events = await query.execute()

    const formattedEvents = events.map(event => ({
      id: event.id,
      title: event.title,
      description: event.description || '',
      eventDate: event.event_date,
      eventTime: event.event_time || '',
      eventType: event.event_type,
      location: event.location || '',
      allDay: event.all_day,
      reminderMinutes: event.reminder_minutes,
      status: event.status,
      createdAt: event.created_at,
      updatedAt: event.updated_at
    }))

    return NextResponse.json({
      success: true,
      events: formattedEvents
    })

  } catch (error) {
    console.error('Get events error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      eventDate,
      eventTime,
      eventType,
      location,
      allDay,
      reminderMinutes
    } = body

    // Validate required fields
    if (!title?.trim()) {
      return NextResponse.json(
        { error: 'Event title is required' },
        { status: 400 }
      )
    }

    if (!eventDate) {
      return NextResponse.json(
        { error: 'Event date is required' },
        { status: 400 }
      )
    }

    if (!eventType || !['appointment', 'breeding', 'health_check', 'show', 'training', 'other'].includes(eventType)) {
      return NextResponse.json(
        { error: 'Valid event type is required' },
        { status: 400 }
      )
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(eventDate)) {
      return NextResponse.json(
        { error: 'Event date must be in YYYY-MM-DD format' },
        { status: 400 }
      )
    }

    // Validate time format if provided
    if (eventTime && !allDay) {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
      if (!timeRegex.test(eventTime)) {
        return NextResponse.json(
          { error: 'Event time must be in HH:MM format' },
          { status: 400 }
        )
      }
    }

    // Create the event
    const event = await db
      .insertInto('events')
      .values({
        breeder_id: session.userId,
        title: title.trim(),
        description: description?.trim() || null,
        event_date: new Date(eventDate),
        event_time: allDay ? null : (eventTime?.trim() || null),
        event_type: eventType,
        location: location?.trim() || null,
        all_day: Boolean(allDay),
        reminder_minutes: reminderMinutes ? parseInt(reminderMinutes) : null,
        status: 'scheduled',
      })
      .returning([
        'id',
        'title',
        'description',
        'event_date',
        'event_time',
        'event_type',
        'location',
        'all_day',
        'reminder_minutes',
        'status',
        'created_at',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return NextResponse.json({
      success: true,
      message: 'Event created successfully',
      event: {
        id: event.id,
        title: event.title,
        description: event.description || '',
        eventDate: event.event_date,
        eventTime: event.event_time || '',
        eventType: event.event_type,
        location: event.location || '',
        allDay: event.all_day,
        reminderMinutes: event.reminder_minutes,
        status: event.status,
        createdAt: event.created_at,
        updatedAt: event.updated_at
      }
    })

  } catch (error) {
    console.error('Create event error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
