import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'

// GET /api/events/[id] - Get a specific event
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const eventId = parseInt(resolvedParams.id)
    
    if (isNaN(eventId)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      )
    }

    const event = await db
      .selectFrom('events')
      .select([
        'id',
        'title',
        'description',
        'event_date',
        'event_time',
        'event_type',
        'location',
        'all_day',
        'reminder_minutes',
        'status',
        'created_at',
        'updated_at'
      ])
      .where('id', '=', eventId)
      .where('breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      )
    }

    const formattedEvent = {
      id: event.id,
      title: event.title,
      description: event.description || '',
      eventDate: event.event_date,
      eventTime: event.event_time || '',
      eventType: event.event_type,
      location: event.location || '',
      allDay: event.all_day,
      reminderMinutes: event.reminder_minutes,
      status: event.status,
      createdAt: event.created_at,
      updatedAt: event.updated_at
    }

    return NextResponse.json({
      success: true,
      event: formattedEvent
    })

  } catch (error) {
    console.error('Get event error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/events/[id] - Update an event
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const eventId = parseInt(resolvedParams.id)
    
    if (isNaN(eventId)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      eventDate,
      eventTime,
      eventType,
      location,
      allDay,
      reminderMinutes,
      status
    } = body

    // Validate required fields
    if (title !== undefined && !title?.trim()) {
      return NextResponse.json(
        { error: 'Event title cannot be empty' },
        { status: 400 }
      )
    }

    if (eventType && !['appointment', 'breeding', 'health_check', 'show', 'training', 'other'].includes(eventType)) {
      return NextResponse.json(
        { error: 'Valid event type is required' },
        { status: 400 }
      )
    }

    if (status && !['scheduled', 'completed', 'cancelled'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required' },
        { status: 400 }
      )
    }

    // Validate date format if provided
    if (eventDate) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/
      if (!dateRegex.test(eventDate)) {
        return NextResponse.json(
          { error: 'Event date must be in YYYY-MM-DD format' },
          { status: 400 }
        )
      }
    }

    // Validate time format if provided
    if (eventTime && !allDay) {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
      if (!timeRegex.test(eventTime)) {
        return NextResponse.json(
          { error: 'Event time must be in HH:MM format' },
          { status: 400 }
        )
      }
    }

    // Check if event exists and belongs to this breeder
    const existingEvent = await db
      .selectFrom('events')
      .select(['id'])
      .where('id', '=', eventId)
      .where('breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!existingEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      )
    }

    // Build update data
    const updateData: Record<string, unknown> = {
      updated_at: new Date()
    }

    if (title !== undefined) {
      updateData.title = title.trim()
    }

    if (description !== undefined) {
      updateData.description = description?.trim() || null
    }

    if (eventDate !== undefined) {
      updateData.event_date = new Date(eventDate)
    }

    if (eventTime !== undefined) {
      updateData.event_time = allDay ? null : (eventTime?.trim() || null)
    }

    if (eventType !== undefined) {
      updateData.event_type = eventType
    }

    if (location !== undefined) {
      updateData.location = location?.trim() || null
    }

    if (allDay !== undefined) {
      updateData.all_day = Boolean(allDay)
      if (allDay) {
        updateData.event_time = null
      }
    }

    if (reminderMinutes !== undefined) {
      updateData.reminder_minutes = reminderMinutes ? parseInt(reminderMinutes) : null
    }

    if (status !== undefined) {
      updateData.status = status
    }

    // Update the event
    const updatedEvent = await db
      .updateTable('events')
      .set(updateData)
      .where('id', '=', eventId)
      .where('breeder_id', '=', session.userId)
      .returning([
        'id',
        'title',
        'description',
        'event_date',
        'event_time',
        'event_type',
        'location',
        'all_day',
        'reminder_minutes',
        'status',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    return NextResponse.json({
      success: true,
      message: 'Event updated successfully',
      event: {
        id: updatedEvent.id,
        title: updatedEvent.title,
        description: updatedEvent.description || '',
        eventDate: updatedEvent.event_date,
        eventTime: updatedEvent.event_time || '',
        eventType: updatedEvent.event_type,
        location: updatedEvent.location || '',
        allDay: updatedEvent.all_day,
        reminderMinutes: updatedEvent.reminder_minutes,
        status: updatedEvent.status,
        updatedAt: updatedEvent.updated_at
      }
    })

  } catch (error) {
    console.error('Update event error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/events/[id] - Delete an event
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const eventId = parseInt(resolvedParams.id)
    
    if (isNaN(eventId)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      )
    }

    // Check if event exists and belongs to this breeder
    const existingEvent = await db
      .selectFrom('events')
      .select(['id'])
      .where('id', '=', eventId)
      .where('breeder_id', '=', session.userId)
      .executeTakeFirst()

    if (!existingEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      )
    }

    // Delete the event
    await db
      .deleteFrom('events')
      .where('id', '=', eventId)
      .where('breeder_id', '=', session.userId)
      .execute()

    return NextResponse.json({
      success: true,
      message: 'Event deleted successfully'
    })

  } catch (error) {
    console.error('Delete event error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
