import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { db } from '@/lib/database'
import { hashPassword } from '@/lib/auth'

export async function GET() {
  try {
    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user profile
    const user = await db
      .selectFrom('users')
      .select([
        'id',
        'email',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zip_code',
        'created_at',
        'updated_at'
      ])
      .where('id', '=', session.userId)
      .executeTakeFirst()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const profile = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      firstName: user.first_name,
      lastName: user.last_name,
      phone: user.phone ?? '',
      streetAddress: user.street_address ?? '',
      aptNumber: user.apt_number ?? '',
      city: user.city ?? '',
      state: user.state ?? '',
      zipCode: user.zip_code ?? '',
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    }

    return NextResponse.json({
      success: true,
      profile
    })

  } catch (error) {
    console.error('Get profile error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      firstName,
      lastName,
      phone,
      streetAddress,
      aptNumber,
      city,
      state,
      zipCode,
      currentPassword,
      newPassword
    } = body

    // Validate required fields
    if (!firstName?.trim() || !lastName?.trim()) {
      return NextResponse.json(
        { error: 'First name and last name are required' },
        { status: 400 }
      )
    }

    // If changing password, validate current password
    if (newPassword) {
      if (!currentPassword) {
        return NextResponse.json(
          { error: 'Current password is required to set a new password' },
          { status: 400 }
        )
      }

      // Verify current password
      const user = await db
        .selectFrom('users')
        .select(['password_hash'])
        .where('id', '=', session.userId)
        .executeTakeFirst()

      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }

      const bcrypt = await import('bcryptjs')
      const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash)
      
      if (!isValidPassword) {
        return NextResponse.json(
          { error: 'Current password is incorrect' },
          { status: 400 }
        )
      }
    }

    // Prepare user update data (excluding business fields)
    const userUpdateData: Record<string, unknown> = {
      first_name: firstName.trim(),
      last_name: lastName.trim(),
      phone: phone?.trim() || null,
      street_address: streetAddress?.trim() || null,
      apt_number: aptNumber?.trim() || null,
      city: city?.trim() || null,
      state: state?.trim() || null,
      zip_code: zipCode?.trim() || null,
      updated_at: new Date(),
    }

    // Add password hash if changing password
    if (newPassword) {
      userUpdateData.password_hash = await hashPassword(newPassword)
    }

    // Update user profile
    const updatedUser = await db
      .updateTable('users')
      .set(userUpdateData)
      .where('id', '=', session.userId)
      .returning([
        'id',
        'email',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zip_code',
        'updated_at'
      ])
      .executeTakeFirstOrThrow()

    const profile = {
      id: updatedUser.id,
      email: updatedUser.email,
      userType: updatedUser.user_type,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      phone: updatedUser.phone ?? '',
      streetAddress: updatedUser.street_address ?? '',
      aptNumber: updatedUser.apt_number ?? '',
      city: updatedUser.city ?? '',
      state: updatedUser.state ?? '',
      zipCode: updatedUser.zip_code ?? '',
      updatedAt: updatedUser.updated_at,
    }

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      profile
    })

  } catch (error) {
    console.error('Update profile error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
