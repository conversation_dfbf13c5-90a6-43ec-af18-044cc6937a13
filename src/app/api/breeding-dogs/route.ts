import { NextResponse } from 'next/server'
import { getSession } from '@/lib/session'
import { getBreedingDogs } from '@/lib/puppies'

// GET /api/breeding-dogs - Get available breeding dogs for the authenticated breeder
export async function GET() {
  try {
    const session = await getSession()
    
    if (!session || session.userType !== 'breeder') {
      return NextResponse.json(
        { error: 'Unauthorized. Breeder access required.' },
        { status: 401 }
      )
    }

    const breedingDogs = await getBreedingDogs(session.userId)
    
    return NextResponse.json({
      success: true,
      breedingDogs
    })
  } catch (error) {
    console.error('Get breeding dogs error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
