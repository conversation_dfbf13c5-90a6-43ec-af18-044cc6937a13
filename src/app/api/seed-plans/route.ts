import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// Define plans directly here to avoid Stripe import issues
const SUBSCRIPTION_PLANS = {
  starter: {
    name: 'Starter',
    priceMonthly: 1900, // $19.00 in cents
    priceYearly: 19000, // $190.00 in cents (2 months free)
    maxLitters: 2,
    maxPuppies: 20,
    maxCustomers: 50,
    maxPhotos: 30,
    maxDocuments: 30,
    features: [
      'Up to 2 litters',
      'Up to 20 puppies',
      'Customer management (50 customers and leads)',
      'Photo storage (30 photos)',
      'Document storage (30 documents)',
      'Basic public website',
      'Calendar & event tracking'
    ]
  },
  professional: {
    name: 'Professional',
    priceMonthly: 4900, // $49.00 in cents
    priceYearly: 49000, // $490.00 in cents (2 months free)
    maxLitters: 10,
    maxPuppies: 100,
    maxCustomers: 150,
    maxPhotos: 200,
    maxDocuments: 200,
    features: [
      'Everything in Starter, plus:',
      'Up to 10 litters',
      'Up to 100 puppies',
      'Customer management (150 customers and leads)',
      'Photo storage (200 photos)',
      'Document storage (200 documents)',
      'Custom branding & logo',
      'Advanced public website',
      'Custom domain support',
      'Detailed analytics & reporting',
      'Waiting list management',
      'Customer relationship tracking'
    ]
  },
  premium: {
    name: 'Premium',
    priceMonthly: 9900, // $99.00 in cents
    priceYearly: 99000, // $990.00 in cents (2 months free)
    maxLitters: null, // unlimited
    maxPuppies: null, // unlimited
    maxCustomers: null, // unlimited
    maxPhotos: 1000,
    maxDocuments: 1000,
    features: [
      'Everything in Professional, plus:',
      'Unlimited litters',
      'Unlimited puppies',
      'Unlimited customers',
      'Photo storage (1000 photos)',
      'Document storage (1000 documents)',
      'Automated email notifications',
      'Pedigree tracking'
    ]
  }
} as const

export async function POST(request: NextRequest) {
  try {
    console.log('Seeding subscription plans...')

    // Clear existing plans
    await db.deleteFrom('subscription_plans').execute()

    // Insert new plans
    for (const [planKey, plan] of Object.entries(SUBSCRIPTION_PLANS)) {
      await db
        .insertInto('subscription_plans')
        .values({
          name: planKey,
          stripe_price_id: `price_${planKey}_monthly`, // Placeholder - replace with actual Stripe price IDs
          stripe_product_id: `prod_${planKey}`, // Placeholder - replace with actual Stripe product IDs
          price_monthly: plan.priceMonthly,
          price_yearly: plan.priceYearly,
          max_litters: plan.maxLitters,
          max_puppies: plan.maxPuppies,
          max_customers: plan.maxCustomers,
          max_photos: plan.maxPhotos,
          max_documents: plan.maxDocuments,
          features: JSON.stringify(plan.features),
        })
        .execute()

      console.log(`✅ Seeded ${plan.name} plan`)
    }

    console.log('✅ Subscription plans seeded successfully!')
    
    return NextResponse.json({ 
      success: true, 
      message: 'Subscription plans seeded successfully' 
    })
  } catch (error) {
    console.error('❌ Error seeding subscription plans:', error)
    return NextResponse.json(
      { error: 'Failed to seed subscription plans' },
      { status: 500 }
    )
  }
}
