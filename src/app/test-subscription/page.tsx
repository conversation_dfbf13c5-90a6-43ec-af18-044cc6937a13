'use client'

import { useState } from 'react'

export default function TestSubscriptionPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState('')

  const testCheckout = async (planType: string, interval: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscriptions/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planType, interval }),
      })

      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testCurrentSubscription = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscriptions/current')
      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Subscription System Test</h1>
      
      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Test Checkout</h2>
          <div className="space-y-4">
            <button
              onClick={() => testCheckout('starter', 'monthly')}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Test Starter Monthly
            </button>
            <button
              onClick={() => testCheckout('professional', 'yearly')}
              disabled={loading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:opacity-50"
            >
              Test Professional Yearly
            </button>
            <button
              onClick={() => testCheckout('premium', 'monthly')}
              disabled={loading}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              Test Premium Monthly
            </button>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Test Current Subscription</h2>
          <button
            onClick={testCurrentSubscription}
            disabled={loading}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 disabled:opacity-50"
          >
            Get Current Subscription
          </button>
        </div>
      </div>

      {result && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-2">Result:</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
            {result}
          </pre>
        </div>
      )}
    </div>
  )
}
