'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { PublicBreederProfile, PublicLitter, PublicPuppy } from '@/lib/breeder-public'

interface BreederData {
  breeder: PublicBreederProfile
  litters: PublicLitter[]
  availablePuppies: PublicPuppy[]
  stats: {
    totalLitters: number
    totalAvailablePuppies: number
    activeLitters: number
  }
}

export default function PublicBreederPage() {
  const params = useParams()
  const breederCode = params.breederCode as string
  
  const [data, setData] = useState<BreederData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (breederCode) {
      loadBreederData()
    }
  }, [breederCode])

  const loadBreederData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/public/breeders/${breederCode}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Breeder not found')
        } else {
          setError('Failed to load breeder information')
        }
        return
      }

      const breederData = await response.json()
      setData(breederData)
    } catch (error) {
      console.error('Load breeder data error:', error)
      setError('Failed to load breeder information')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'Not specified'
    return new Date(date).toLocaleDateString()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatSizeRange = (min?: number, max?: number) => {
    if (!min && !max) return 'Not specified'
    if (min && max) return `${min} - ${max} lbs`
    if (min) return `${min}+ lbs`
    if (max) return `Up to ${max} lbs`
    return 'Not specified'
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Breeder not found'}
          </h1>
          <p className="text-gray-600">
            The breeder you're looking for doesn't exist or isn't available.
          </p>
        </div>
      </div>
    )
  }

  const { breeder, litters, availablePuppies, stats } = data

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              {breeder.logoUrl ? (
                <div className="flex items-center">
                  <Image
                    src={breeder.logoUrl}
                    alt={`${breeder.businessName} Logo`}
                    width={40}
                    height={40}
                    className="mr-3 rounded-lg object-contain"
                  />
                  <h1 className="text-2xl font-bold text-gray-900">{breeder.businessName}</h1>
                </div>
              ) : (
                <div className="flex items-center">
                  <Image
                    src="/pawledger_logo.png"
                    alt="PawLedger Logo"
                    width={40}
                    height={40}
                    className="mr-3"
                  />
                  <h1 className="text-2xl font-bold text-gray-900">{breeder.businessName}</h1>
                </div>
              )}
            </div>
            <div className="text-sm text-gray-600">
              Powered by PawLedger
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              {breeder.businessName}
            </h1>
            <p className="text-xl mb-8">
              Professional Dog Breeder
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalLitters}</div>
                <div className="text-blue-200">Total Litters</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalAvailablePuppies}</div>
                <div className="text-blue-200">Available Puppies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.activeLitters}</div>
                <div className="text-blue-200">Active Litters</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact Information</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {breeder.businessPhone && (
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">Business Phone</h3>
                <p className="text-gray-600">{breeder.businessPhone}</p>
              </div>
            )}
            {breeder.phone && (
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">Mobile Phone</h3>
                <p className="text-gray-600">{breeder.phone}</p>
              </div>
            )}
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900">Email</h3>
              <p className="text-gray-600">{breeder.email}</p>
            </div>
            {(breeder.city || breeder.state) && (
              <div className="text-center">
                <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">Location</h3>
                <p className="text-gray-600">
                  {[breeder.city, breeder.state].filter(Boolean).join(', ')}
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Available Puppies */}
      {availablePuppies.length > 0 && (
        <section className="py-12 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Available Puppies</h2>
              <p className="text-gray-600">Find your perfect companion</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {availablePuppies.map((puppy) => (
                <div key={puppy.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  {puppy.photos.length > 0 && puppy.photos[0].url && (
                    <div className="h-48 bg-gray-200 relative">
                      <Image
                        src={puppy.photos[0].url}
                        alt={puppy.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{puppy.name}</h3>
                    {puppy.description && (
                      <p className="text-gray-600 mb-4">{puppy.description}</p>
                    )}
                    <div className="space-y-2 text-sm">
                      {puppy.color && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Color:</span>
                          <span className="text-gray-900">{puppy.color}</span>
                        </div>
                      )}
                      {puppy.birthDate && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Born:</span>
                          <span className="text-gray-900">{formatDate(puppy.birthDate)}</span>
                        </div>
                      )}
                      {puppy.litterCode && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Litter:</span>
                          <span className="text-gray-900">{puppy.litterCode}</span>
                        </div>
                      )}
                      {puppy.price && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Price:</span>
                          <span className="text-green-600 font-semibold">{formatCurrency(puppy.price)}</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Available
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Litters */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Litters</h2>
            <p className="text-gray-600">Browse our current and past litters</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {litters.map((litter) => (
              <Link
                key={litter.id}
                href={`/${breederCode}/litters/${litter.id}`}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">{litter.litterCode}</h3>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      litter.status === 'READY_TO_GO_HOME' ? 'bg-green-100 text-green-800' :
                      litter.status === 'BORN' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {litter.status.replace('_', ' ')}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    {litter.color && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Color:</span>
                        <span className="text-gray-900">{litter.color}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-500">Expected Size:</span>
                      <span className="text-gray-900">{formatSizeRange(litter.minExpectedSizeLbs, litter.maxExpectedSizeLbs)}</span>
                    </div>
                    {litter.actualBirthDate && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Born:</span>
                        <span className="text-gray-900">{formatDate(litter.actualBirthDate)}</span>
                      </div>
                    )}
                    {litter.expectedBirthDate && !litter.actualBirthDate && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Expected:</span>
                        <span className="text-gray-900">{formatDate(litter.expectedBirthDate)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-500">Puppies:</span>
                      <span className="text-gray-900">{litter.puppyCount} total, {litter.availablePuppyCount} available</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 text-blue-600 text-sm font-medium">
                    View Details →
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-400">
              © 2024 {breeder.businessName}. Powered by PawLedger.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
