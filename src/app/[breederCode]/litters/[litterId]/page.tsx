'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { PublicBreederProfile, PublicPuppy } from '@/lib/breeder-public'

interface LitterData {
  id: number
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color?: string
  momName?: string
  dadName?: string
  createdAt: Date
  photos: Array<{
    id: number
    filename: string
    url: string
  }>
  puppies: PublicPuppy[]
  stats: {
    totalPuppies: number
    availablePuppies: number
    soldPuppies: number
  }
}

interface LitterPageData {
  breeder: PublicBreederProfile
  litter: LitterData
}

export default function PublicLitterPage() {
  const params = useParams()
  const router = useRouter()
  const breederCode = params.breederCode as string
  const litterId = params.litterId as string
  
  const [data, setData] = useState<LitterPageData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (breederCode && litterId) {
      loadLitterData()
    }
  }, [breederCode, litterId])

  const loadLitterData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/public/breeders/${breederCode}/litters/${litterId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Litter not found')
        } else {
          setError('Failed to load litter information')
        }
        return
      }

      const litterData = await response.json()
      setData(litterData)
    } catch (error) {
      console.error('Load litter data error:', error)
      setError('Failed to load litter information')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'Not specified'
    return new Date(date).toLocaleDateString()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatSizeRange = (min?: number, max?: number) => {
    if (!min && !max) return 'Not specified'
    if (min && max) return `${min} - ${max} lbs`
    if (min) return `${min}+ lbs`
    if (max) return `Up to ${max} lbs`
    return 'Not specified'
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Litter not found'}
          </h1>
          <p className="text-gray-600 mb-4">
            The litter you're looking for doesn't exist or isn't available.
          </p>
          <Link
            href={`/${breederCode}`}
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            ← Back to Breeder Profile
          </Link>
        </div>
      </div>
    )
  }

  const { breeder, litter } = data

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              {breeder.logoUrl ? (
                <div className="flex items-center">
                  <Image
                    src={breeder.logoUrl}
                    alt={`${breeder.businessName} Logo`}
                    width={40}
                    height={40}
                    className="mr-3 rounded-lg object-contain"
                  />
                  <h1 className="text-2xl font-bold text-gray-900">{breeder.businessName}</h1>
                </div>
              ) : (
                <div className="flex items-center">
                  <Image
                    src="/pawledger_logo.png"
                    alt="PawLedger Logo"
                    width={40}
                    height={40}
                    className="mr-3"
                  />
                  <h1 className="text-2xl font-bold text-gray-900">{breeder.businessName}</h1>
                </div>
              )}
            </div>
            <Link
              href={`/${breederCode}`}
              className="text-blue-600 hover:text-blue-500 font-medium"
            >
              ← Back to {breeder.businessName}
            </Link>
          </div>
        </div>
      </header>

      {/* Litter Hero */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Litter {litter.litterCode}
            </h1>
            <p className="text-xl mb-8">
              {breeder.businessName}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{litter.stats.totalPuppies}</div>
                <div className="text-blue-200">Total Puppies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{litter.stats.availablePuppies}</div>
                <div className="text-blue-200">Available</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{litter.stats.soldPuppies}</div>
                <div className="text-blue-200">Sold</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Litter Information */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Litter Photos */}
            {litter.photos.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Litter Photos</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {litter.photos.map((photo) => (
                    photo.url && (
                      <div key={photo.id} className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                        <Image
                          src={photo.url}
                          alt={`Litter ${litter.litterCode}`}
                          width={400}
                          height={400}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )
                  ))}
                </div>
              </div>
            )}

            {/* Litter Details */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Litter Details</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                      litter.status === 'READY_TO_GO_HOME' ? 'bg-green-100 text-green-800' :
                      litter.status === 'BORN' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {litter.status.replace('_', ' ')}
                    </span>
                  </div>
                  
                  {litter.color && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Color:</span>
                      <span className="text-gray-900">{litter.color}</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expected Size:</span>
                    <span className="text-gray-900">{formatSizeRange(litter.minExpectedSizeLbs, litter.maxExpectedSizeLbs)}</span>
                  </div>
                  
                  {litter.actualBirthDate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Birth Date:</span>
                      <span className="text-gray-900">{formatDate(litter.actualBirthDate)}</span>
                    </div>
                  )}
                  
                  {litter.expectedBirthDate && !litter.actualBirthDate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Expected Birth:</span>
                      <span className="text-gray-900">{formatDate(litter.expectedBirthDate)}</span>
                    </div>
                  )}
                  
                  {litter.momName && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Mother:</span>
                      <span className="text-gray-900">{litter.momName}</span>
                    </div>
                  )}
                  
                  {litter.dadName && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Father:</span>
                      <span className="text-gray-900">{litter.dadName}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Puppies */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Puppies in this Litter</h2>
            <p className="text-gray-600">Meet the adorable puppies from litter {litter.litterCode}</p>
          </div>
          
          {litter.puppies.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600">No puppies have been added to this litter yet.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {litter.puppies.map((puppy) => (
                <div key={puppy.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  {puppy.photos.length > 0 && puppy.photos[0].url && (
                    <div className="h-48 bg-gray-200 relative">
                      <Image
                        src={puppy.photos[0].url}
                        alt={puppy.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{puppy.name}</h3>
                    {puppy.description && (
                      <p className="text-gray-600 mb-4">{puppy.description}</p>
                    )}
                    <div className="space-y-2 text-sm">
                      {puppy.color && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Color:</span>
                          <span className="text-gray-900">{puppy.color}</span>
                        </div>
                      )}
                      {puppy.birthDate && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Born:</span>
                          <span className="text-gray-900">{formatDate(puppy.birthDate)}</span>
                        </div>
                      )}
                      {puppy.price && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Price:</span>
                          <span className="text-green-600 font-semibold">{formatCurrency(puppy.price)}</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        puppy.isAvailable 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {puppy.isAvailable ? 'Available' : 'Sold'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Interested in a Puppy?</h2>
            <p className="text-gray-600">Contact {breeder.businessName} for more information</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            {breeder.businessPhone && (
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">Business Phone</h3>
                <p className="text-gray-600">{breeder.businessPhone}</p>
              </div>
            )}
            {breeder.phone && (
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">Mobile Phone</h3>
                <p className="text-gray-600">{breeder.phone}</p>
              </div>
            )}
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900">Email</h3>
              <p className="text-gray-600">{breeder.email}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-400">
              © 2024 {breeder.businessName}. Powered by PawLedger.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
