@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Improve text contrast - replace light grey text with darker, more readable colors */
.text-gray-600 {
  color: #374151 !important; /* was #6b7280, now darker grey-700 */
}

.text-gray-500 {
  color: #4b5563 !important; /* was #6b7280, now darker grey-600 */
}

.text-gray-400 {
  color: #6b7280 !important; /* was #9ca3af, now grey-500 */
}

/* Ensure good contrast on white backgrounds */
.bg-white .text-gray-600,
.bg-white .text-gray-500,
.bg-white .text-gray-400 {
  color: #374151 !important; /* Force dark grey on white backgrounds */
}

/* Ensure good contrast on light grey backgrounds */
.bg-gray-50 .text-gray-600,
.bg-gray-100 .text-gray-600 {
  color: #1f2937 !important; /* Even darker on light grey backgrounds */
}

/* Improve small text readability */
.text-sm.text-gray-600,
.text-xs.text-gray-600 {
  color: #1f2937 !important; /* Darker for small text */
}

/* Ensure status badges have good contrast */
.bg-gray-100.text-gray-800 {
  background-color: #e5e7eb !important;
  color: #1f2937 !important;
}

/* Fix placeholder text contrast */
.placeholder-gray-500::placeholder {
  color: #6b7280 !important; /* Darker than default */
}

/* Fix helper text contrast */
.text-gray-500 {
  color: #4b5563 !important; /* Darker grey-600 instead of grey-500 */
}

/* Fix disabled text contrast */
.disabled\:text-gray-500:disabled {
  color: #6b7280 !important; /* Darker for disabled text */
}

/* Fix form labels */
.text-gray-700 {
  color: #374151 !important; /* Keep dark for labels */
}

/* Fix navigation text */
.text-gray-400 {
  color: #6b7280 !important; /* Darker grey-500 instead of grey-400 */
}

/* Fix specific UI component text */
.bg-gray-50 .text-gray-600,
.bg-gray-50 .text-gray-500 {
  color: #1f2937 !important; /* Very dark on light grey backgrounds */
}

/* Fix loading skeleton text */
.animate-pulse .text-gray-600 {
  color: #374151 !important;
}
