'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { SubscriptionManager } from '@/components/subscriptions/SubscriptionManager'
import { ErrorBoundary } from '@/components/ui/ErrorBoundary'

export default function SubscriptionsPage() {
  return (
    <DashboardLayout userType="breeder">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subscription Management</h1>
          <p className="mt-2 text-gray-800">
            Manage your PawLedger subscription, view usage, and upgrade your plan.
          </p>
        </div>

        {/* Subscription Manager */}
        <ErrorBoundary fallback={
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">Subscription Manager Unavailable</h3>
            <p className="text-yellow-700">
              The subscription management feature is temporarily unavailable. Please try refreshing the page.
            </p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
            >
              Refresh Page
            </button>
          </div>
        }>
          <SubscriptionManager />
        </ErrorBoundary>

        {/* Additional Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">Need Help?</h3>
          <div className="space-y-2 text-blue-700">
            <p>• All plans include a 14-day free trial</p>
            <p>• You can upgrade or downgrade at any time</p>
            <p>• Annual plans save you 2 months (equivalent to 10 months pricing)</p>
            <p>• Usage limits reset monthly on your billing date</p>
            <p>• Contact support if you need assistance with your subscription</p>
          </div>
        </div>

        {/* Billing Information */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Billing Information</h3>
          <div className="text-gray-800 space-y-2">
            <p>• Billing is handled securely through Stripe</p>
            <p>• You'll receive email receipts for all payments</p>
            <p>• Cancel anytime - no long-term commitments</p>
            <p>• Prorated billing when upgrading mid-cycle</p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
