'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Button } from '@/components/ui/Button'

interface Puppy {
  id: number
  name: string
  description?: string
  birthDate?: Date
  color?: string
  litterId?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
  createdAt: Date
  updatedAt: Date
}

interface Litter {
  id: number
  litterCode: string
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
}

interface Owner {
  id: number
  firstName: string
  lastName: string
  email: string
  userType: 'breeder' | 'customer'
}

interface Photo {
  id: number
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: Date
}

interface PuppyDetailPageProps {
  params: Promise<{ id: string }>
}

export default function PuppyDetailPage({ params }: PuppyDetailPageProps) {
  const router = useRouter()
  const [puppy, setPuppy] = useState<Puppy | null>(null)
  const [litter, setLitter] = useState<Litter | null>(null)
  const [owner, setOwner] = useState<Owner | null>(null)
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [puppyId, setPuppyId] = useState<string | null>(null)

  // Resolve params
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params
      setPuppyId(resolvedParams.id)
    }
    resolveParams()
  }, [params])

  // Load puppy details
  useEffect(() => {
    if (!puppyId) return

    const loadPuppyDetails = async () => {
      try {
        setLoading(true)
        
        // Load puppy data
        const puppyResponse = await fetch(`/api/puppies/${puppyId}`)
        if (!puppyResponse.ok) {
          throw new Error('Failed to load puppy')
        }
        const puppyData = await puppyResponse.json()
        setPuppy(puppyData.puppy)

        // Load litter data if puppy has a litter
        if (puppyData.puppy.litterId) {
          const litterResponse = await fetch(`/api/litters/${puppyData.puppy.litterId}`)
          if (litterResponse.ok) {
            const litterData = await litterResponse.json()
            setLitter(litterData.litter)
          }
        }

        // Load owner data if puppy has an owner
        if (puppyData.puppy.ownerId) {
          const ownerResponse = await fetch(`/api/users/${puppyData.puppy.ownerId}`)
          if (ownerResponse.ok) {
            const ownerData = await ownerResponse.json()
            setOwner(ownerData.user)
          }
        }

        // Load photos for this puppy
        const photosResponse = await fetch(`/api/photos?entityType=puppy&entityId=${puppyId}`)
        if (photosResponse.ok) {
          const photosData = await photosResponse.json()
          setPhotos(photosData.photos || [])
        }

      } catch (error) {
        setError('Failed to load puppy details')
        console.error('Load puppy details error:', error)
      } finally {
        setLoading(false)
      }
    }

    loadPuppyDetails()
  }, [puppyId])

  const getAge = (birthDate: Date | undefined) => {
    if (!birthDate) return 'Unknown'
    
    const birth = new Date(birthDate)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - birth.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 7) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} old`
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7)
      return `${weeks} week${weeks === 1 ? '' : 's'} old`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} month${months === 1 ? '' : 's'} old`
    } else {
      const years = Math.floor(diffDays / 365)
      return `${years} year${years === 1 ? '' : 's'} old`
    }
  }

  const getOwnershipBadge = (ownerType?: string) => {
    if (!ownerType) return null

    const config = {
      'breeder': { label: 'Available', className: 'bg-blue-100 text-blue-800' },
      'customer': { label: 'Sold', className: 'bg-green-100 text-green-800' },
    }

    const ownerConfig = config[ownerType as keyof typeof config]
    if (!ownerConfig) return null

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ownerConfig.className}`}>
        {ownerConfig.label}
      </span>
    )
  }

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Not set'
    return new Date(date).toLocaleDateString()
  }

  const handleEdit = () => {
    router.push(`/breeder/puppies?edit=${puppyId}`)
  }

  const handleBack = () => {
    router.push('/breeder/puppies')
  }

  const handleViewLitter = () => {
    if (litter) {
      router.push(`/breeder/litters/${litter.id}`)
    }
  }

  if (loading) {
    return (
      <DashboardLayout userType="breeder" title="Puppy Details">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !puppy) {
    return (
      <DashboardLayout userType="breeder" title="Puppy Details">
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error || 'Puppy not found'}</div>
          <Button onClick={handleBack}>Back to Puppies</Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userType="breeder" title={`Puppy: ${puppy.name}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{puppy.name}</h2>
            <p className="text-gray-600">Puppy profile and information</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleBack}>
              Back to Puppies
            </Button>
            <Button onClick={handleEdit}>
              Edit Puppy
            </Button>
          </div>
        </div>

        {/* Photo Gallery */}
        {photos.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Puppy Photos ({photos.length})</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {photos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <img
                    src={photo.url}
                    alt={photo.originalName}
                    className="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                    onClick={() => window.open(photo.url, '_blank')}
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                    <p className="truncate">{photo.originalName}</p>
                    <p>{new Date(photo.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Puppy Information */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Puppy Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500">Name</label>
              <p className="mt-1 text-sm text-gray-900 font-medium">{puppy.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Color</label>
              <p className="mt-1 text-sm text-gray-900">{puppy.color || 'Not specified'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">{getOwnershipBadge(puppy.ownerType)}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Birth Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(puppy.birthDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Age</label>
              <p className="mt-1 text-sm text-gray-900">{getAge(puppy.birthDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Created</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(puppy.createdAt)}</p>
            </div>
          </div>
          
          {puppy.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-500">Description</label>
              <p className="mt-1 text-sm text-gray-900">{puppy.description}</p>
            </div>
          )}
        </div>

        {/* Litter Information */}
        {litter && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Litter Information</h3>
              <Button size="sm" variant="outline" onClick={handleViewLitter}>
                View Litter
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Litter Code</label>
                <p className="mt-1 text-sm text-gray-900">{litter.litterCode}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Litter Status</label>
                <p className="mt-1 text-sm text-gray-900">{litter.status.replace('_', ' ')}</p>
              </div>
            </div>
          </div>
        )}

        {/* Owner Information */}
        {owner && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Owner Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Owner Name</label>
                <p className="mt-1 text-sm text-gray-900">{owner.firstName} {owner.lastName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1 text-sm text-gray-900">{owner.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Owner Type</label>
                <p className="mt-1 text-sm text-gray-900 capitalize">{owner.userType}</p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Actions</h3>
          <div className="flex flex-wrap gap-3">
            <Button onClick={handleEdit}>
              Edit Puppy Profile
            </Button>
            {litter && (
              <Button variant="outline" onClick={handleViewLitter}>
                View Litter Details
              </Button>
            )}
            {puppy.ownerType === 'breeder' && (
              <Button variant="outline">
                Mark as Sold
              </Button>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
