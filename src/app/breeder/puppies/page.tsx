'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Button } from '@/components/ui/Button'
import { PuppyList } from '@/components/puppies/PuppyList'
import { PuppyForm } from '@/components/puppies/PuppyForm'

interface PuppyFormData {
  name: string
  description: string
  birthDate: string
  color: string
  price: number | undefined
  litterId: string
  ownerId: string
  ownerType: 'breeder' | 'customer'
}

interface Puppy {
  id: number
  name: string
  description?: string
  birthDate?: Date
  color?: string
  litterId?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
  createdAt: Date
}

export default function PuppiesPage() {
  const [puppies, setPuppies] = useState<Puppy[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingPuppy, setEditingPuppy] = useState<Puppy | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [preselectedLitterId, setPreselectedLitterId] = useState<string | null>(null)

  // Load puppies
  const loadPuppies = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/puppies')
      if (response.ok) {
        const data = await response.json()
        setPuppies(data.puppies || [])
      } else {
        setError('Failed to load puppies')
      }
    } catch (error) {
      setError('Failed to load puppies')
      console.error('Load puppies error:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadPuppies()

    // Check for litterId parameter to pre-select litter
    const urlParams = new URLSearchParams(window.location.search)
    const litterId = urlParams.get('litterId')
    if (litterId) {
      setPreselectedLitterId(litterId)
      setShowForm(true)
      // Clean up URL
      window.history.replaceState({}, '', '/breeder/puppies')
    }
  }, [])

  // Handle URL edit parameter
  useEffect(() => {
    if (puppies.length > 0) {
      const urlParams = new URLSearchParams(window.location.search)
      const editId = urlParams.get('edit')
      if (editId) {
        const puppyToEdit = puppies.find(p => p.id.toString() === editId)
        if (puppyToEdit) {
          handleEdit(puppyToEdit)
        }
        // Clean up URL
        window.history.replaceState({}, '', '/breeder/puppies')
      }
    }
  }, [puppies])

  // Handle form submission
  const handleFormSubmit = async (formData: any) => {
    try {
      const url = editingPuppy ? `/api/puppies/${editingPuppy.id}` : '/api/puppies'
      const method = editingPuppy ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        await loadPuppies()
        setShowForm(false)
        setEditingPuppy(null)
        setError(null)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to save puppy')
      }
    } catch (error) {
      setError('Failed to save puppy')
      console.error('Save puppy error:', error)
    }
  }

  // Handle edit
  const handleEdit = (puppy: Puppy) => {
    setEditingPuppy(puppy)
    setShowForm(true)
    setError(null)
  }

  // Handle delete
  const handleDelete = async (puppy: Puppy) => {
    if (!confirm(`Are you sure you want to delete "${puppy.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/puppies/${puppy.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await loadPuppies()
        setError(null)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to delete puppy')
      }
    } catch (error) {
      setError('Failed to delete puppy')
      console.error('Delete puppy error:', error)
    }
  }

  // Handle view
  const handleView = (puppy: Puppy) => {
    window.location.href = `/breeder/puppies/${puppy.id}`
  }

  // Handle cancel form
  const handleCancel = () => {
    setShowForm(false)
    setEditingPuppy(null)
    setPreselectedLitterId(null)
    setError(null)
  }

  // Handle add new puppy
  const handleAddNew = () => {
    setEditingPuppy(null)
    setPreselectedLitterId(null)
    setShowForm(true)
    setError(null)
  }

  // Convert puppy data for form
  const getFormData = (puppy: Puppy | null) => {
    if (!puppy) return undefined

    return {
      name: puppy.name,
      description: puppy.description || '',
      birthDate: puppy.birthDate ? new Date(puppy.birthDate).toISOString().split('T')[0] : '',
      color: puppy.color || '',
      litterId: puppy.litterId?.toString() || '',
      ownerId: puppy.ownerId?.toString() || '',
      ownerType: puppy.ownerType || 'breeder',
    }
  }

  return (
    <DashboardLayout userType="breeder" title="Puppy Management">
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {showForm ? (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {editingPuppy ? 'Edit Puppy Profile' : 'Add New Puppy'}
            </h2>
          </div>
          <PuppyForm
            initialData={getFormData(editingPuppy)}
            onSubmit={handleFormSubmit}
            onCancel={handleCancel}
            submitLabel={editingPuppy ? 'Update Puppy' : 'Create Puppy'}
            puppyId={editingPuppy?.id}
            preselectedLitterId={preselectedLitterId}
          />
        </div>
      ) : (
        <div>
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Your Puppies</h2>
              <p className="text-gray-600">Manage puppy profiles and track ownership.</p>
            </div>
            <Button onClick={handleAddNew}>
              Add New Puppy
            </Button>
          </div>

          <PuppyList
            puppies={puppies}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onView={handleView}
            loading={loading}
            showOwnership={true}
          />
        </div>
      )}
    </DashboardLayout>
  )
}
