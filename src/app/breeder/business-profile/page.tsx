'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { LogoUpload } from '@/components/breeder/LogoUpload'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import { useUser } from '@/contexts/UserContext'
import { Breeder } from '@/lib/breeders'

const US_STATES = [
  { value: '', label: 'Select State' },
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
]

interface FormErrors {
  [key: string]: string
}

export default function BusinessProfilePage() {
  const { user } = useUser()
  const [breeder, setBreeder] = useState<Breeder | null>(null)
  const [originalBreeder, setOriginalBreeder] = useState<Breeder | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    if (user) {
      loadBreederInfo()
    }
  }, [user])

  useEffect(() => {
    // Check if there are changes compared to original data
    if (originalBreeder && breeder) {
      const hasChanges = JSON.stringify(originalBreeder) !== JSON.stringify(breeder)
      setHasChanges(hasChanges)
    }
  }, [breeder, originalBreeder])

  const loadBreederInfo = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/breeders/me')
      if (response.ok) {
        const data = await response.json()
        setBreeder(data.breeder)
        setOriginalBreeder(data.breeder)
      } else {
        setMessage({ type: 'error', text: 'Failed to load breeder information' })
      }
    } catch (error) {
      console.error('Load breeder info error:', error)
      setMessage({ type: 'error', text: 'Failed to load breeder information' })
    } finally {
      setLoading(false)
    }
  }

  const handleLogoUpdate = (logoUrl: string | null) => {
    if (breeder) {
      setBreeder({
        ...breeder,
        logoUrl: logoUrl || undefined
      })
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }

    let processedValue: string | number | undefined = value

    // Special processing for different field types
    if (name === 'yearsExperience') {
      processedValue = value ? parseInt(value) : undefined
    } else if (name === 'businessPhone') {
      // Auto-format phone number as user types
      const digitsOnly = value.replace(/\D/g, '')
      if (digitsOnly.length <= 10) {
        if (digitsOnly.length >= 6) {
          processedValue = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`
        } else if (digitsOnly.length >= 3) {
          processedValue = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3)}`
        } else {
          processedValue = digitsOnly
        }
      } else {
        processedValue = breeder?.businessPhone || '' // Don't allow more than 10 digits
      }
    } else if (name === 'businessZipCode') {
      // Only allow digits and hyphens for ZIP code
      processedValue = value.replace(/[^\d-]/g, '')
    } else if (name === 'website') {
      // Auto-add https:// if user enters a domain without protocol
      if (value && !value.startsWith('http://') && !value.startsWith('https://') && value.includes('.')) {
        processedValue = `https://${value}`
      }
    }

    if (breeder) {
      setBreeder({
        ...breeder,
        [name]: processedValue
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Required fields
    if (!breeder?.businessName?.trim()) {
      newErrors.businessName = 'Business name is required'
    } else if (breeder.businessName.trim().length < 2) {
      newErrors.businessName = 'Business name must be at least 2 characters'
    } else if (breeder.businessName.trim().length > 100) {
      newErrors.businessName = 'Business name must be less than 100 characters'
    }

    // Phone validation
    if (breeder?.businessPhone) {
      const phoneRegex = /^\(\d{3}\) \d{3}-\d{4}$/
      const digitsOnly = breeder.businessPhone.replace(/\D/g, '')

      if (!phoneRegex.test(breeder.businessPhone) && digitsOnly.length === 10) {
        // Auto-format if it's 10 digits
        const formatted = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`
        setBreeder(prev => prev ? { ...prev, businessPhone: formatted } : null)
      } else if (!phoneRegex.test(breeder.businessPhone)) {
        newErrors.businessPhone = 'Phone number must be in format (*************'
      }
    }

    // Website validation
    if (breeder?.website) {
      const websiteRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
      if (!websiteRegex.test(breeder.website)) {
        newErrors.website = 'Please enter a valid website URL (e.g., https://www.example.com)'
      }
    }

    // Years of experience validation
    if (breeder?.yearsExperience !== undefined && breeder.yearsExperience !== null) {
      if (breeder.yearsExperience < 0) {
        newErrors.yearsExperience = 'Years of experience cannot be negative'
      } else if (breeder.yearsExperience > 100) {
        newErrors.yearsExperience = 'Years of experience must be 100 or less'
      }
    }

    // ZIP code validation
    if (breeder?.businessZipCode) {
      const zipRegex = /^\d{5}(-\d{4})?$/
      if (!zipRegex.test(breeder.businessZipCode)) {
        newErrors.businessZipCode = 'ZIP code must be in format 12345 or 12345-6789'
      }
    }

    // Business address validation (if any address field is filled, require city and state)
    const hasAddressInfo = breeder?.businessStreetAddress || breeder?.businessCity ||
                          breeder?.businessState || breeder?.businessZipCode

    if (hasAddressInfo) {
      if (!breeder?.businessCity?.trim()) {
        newErrors.businessCity = 'City is required when address is provided'
      }
      if (!breeder?.businessState?.trim()) {
        newErrors.businessState = 'State is required when address is provided'
      }
    }

    // Specialties validation
    if (breeder?.specialties && breeder.specialties.length > 200) {
      newErrors.specialties = 'Specialties must be less than 200 characters'
    }

    // Description validation
    if (breeder?.description && breeder.description.length > 1000) {
      newErrors.description = 'Description must be less than 1000 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!breeder || !validateForm()) return

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch('/api/breeders/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: breeder.businessName,
          businessPhone: breeder.businessPhone,
          businessStreetAddress: breeder.businessStreetAddress,
          businessAptNumber: breeder.businessAptNumber,
          businessCity: breeder.businessCity,
          businessState: breeder.businessState,
          businessZipCode: breeder.businessZipCode,
          website: breeder.website,
          description: breeder.description,
          specialties: breeder.specialties,
          yearsExperience: breeder.yearsExperience,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update business profile')
      }

      const data = await response.json()
      setBreeder(data.breeder)
      setOriginalBreeder(data.breeder)
      setHasChanges(false)
      setMessage({ type: 'success', text: 'Business profile updated successfully!' })
      
      // Clear message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error) {
      console.error('Save business profile error:', error)
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to update business profile' 
      })
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    if (originalBreeder) {
      setBreeder({ ...originalBreeder })
      setErrors({})
      setMessage(null)
    }
  }

  if (loading) {
    return (
      <DashboardLayout userType="breeder">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (!breeder) {
    return (
      <DashboardLayout userType="breeder">
        <div className="text-center py-12">
          <p className="text-gray-500">Breeder information not found.</p>
          <Button
            onClick={loadBreederInfo}
            className="mt-4"
          >
            Retry
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userType="breeder">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Business Profile</h1>
            <p className="mt-2 text-gray-600">
              Manage your business information, logo, and public profile details.
            </p>
          </div>
          
          {hasChanges && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-amber-600 font-medium">Unsaved changes</span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={saving}
              >
                Reset
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving}
                size="sm"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          )}
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{message.text}</p>
              </div>
            </div>
          </div>
        )}

        {/* Logo Management Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <LogoUpload
            currentLogoUrl={breeder.logoUrl}
            onLogoUpdate={handleLogoUpdate}
            businessName={breeder.businessName}
          />
        </div>

        {/* Business Information Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
            <p className="mt-1 text-sm text-gray-500">
              This information will be displayed on your public breeder profile.
            </p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* Basic Business Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Business Name *"
                name="businessName"
                value={breeder.businessName || ''}
                onChange={handleInputChange}
                error={errors.businessName}
                placeholder="ABC Dog Breeding"
                required
              />

              <Input
                label="Business Phone"
                name="businessPhone"
                type="tel"
                value={breeder.businessPhone || ''}
                onChange={handleInputChange}
                error={errors.businessPhone}
                placeholder="(*************"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Website"
                name="website"
                type="url"
                value={breeder.website || ''}
                onChange={handleInputChange}
                error={errors.website}
                placeholder="https://www.yourbusiness.com"
              />

              <Input
                label="Years of Experience"
                name="yearsExperience"
                type="number"
                min="0"
                max="100"
                value={breeder.yearsExperience?.toString() || ''}
                onChange={handleInputChange}
                error={errors.yearsExperience}
                placeholder="5"
              />
            </div>

            <div>
              <Input
                label="Specialties"
                name="specialties"
                value={breeder.specialties || ''}
                onChange={handleInputChange}
                error={errors.specialties}
                placeholder="Golden Retrievers, Labrador Retrievers, Family Dogs"
                helpText="List the dog breeds or types you specialize in"
              />
              <div className="mt-1 text-right text-xs text-gray-500">
                {(breeder.specialties || '').length}/200 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Description
              </label>
              <textarea
                name="description"
                value={breeder.description || ''}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Tell customers about your breeding program, experience, and what makes your dogs special. This will be displayed on your public profile."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
              <div className="mt-1 flex justify-between text-xs text-gray-500">
                <span>Describe your breeding philosophy, experience, and what sets your program apart.</span>
                <span>{(breeder.description || '').length}/1000 characters</span>
              </div>
            </div>
          </div>
        </div>

        {/* Business Address Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Business Address</h2>
            <p className="mt-1 text-sm text-gray-500">
              Your business location information for customers and official records.
            </p>
          </div>
          
          <div className="p-6 space-y-6">
            <Input
              label="Street Address"
              name="businessStreetAddress"
              value={breeder.businessStreetAddress || ''}
              onChange={handleInputChange}
              placeholder="123 Business Street"
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Input
                label="Apartment/Unit Number"
                name="businessAptNumber"
                value={breeder.businessAptNumber || ''}
                onChange={handleInputChange}
                placeholder="Suite 100"
              />

              <Input
                label="City"
                name="businessCity"
                value={breeder.businessCity || ''}
                onChange={handleInputChange}
                placeholder="Business City"
              />

              <div className="grid grid-cols-2 gap-3">
                <Select
                  label="State"
                  name="businessState"
                  value={breeder.businessState || ''}
                  onChange={handleInputChange}
                  options={US_STATES}
                />

                <Input
                  label="ZIP Code"
                  name="businessZipCode"
                  value={breeder.businessZipCode || ''}
                  onChange={handleInputChange}
                  error={errors.businessZipCode}
                  placeholder="12345"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pb-8">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={saving || !hasChanges}
          >
            Reset Changes
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || !hasChanges}
            className="px-8"
          >
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Business Profile'
            )}
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
}
