'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Button } from '@/components/ui/Button'
import { LitterList } from '@/components/litters/LitterList'
import { LitterForm } from '@/components/litters/LitterForm'

interface Litter {
  id: number
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color?: string
  momId?: number
  dadId?: number
  createdAt: Date
}

export default function LittersPage() {
  const [litters, setLitters] = useState<Litter[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingLitter, setEditingLitter] = useState<Litter | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Load litters
  const loadLitters = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/litters')
      if (response.ok) {
        const data = await response.json()
        setLitters(data.litters || [])
      } else {
        setError('Failed to load litters')
      }
    } catch (error) {
      setError('Failed to load litters')
      console.error('Load litters error:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadLitters()
  }, [])

  // Handle URL edit parameter
  useEffect(() => {
    if (litters.length > 0) {
      const urlParams = new URLSearchParams(window.location.search)
      const editId = urlParams.get('edit')
      if (editId) {
        const litterToEdit = litters.find(l => l.id.toString() === editId)
        if (litterToEdit) {
          handleEdit(litterToEdit)
        }
        // Clean up URL
        window.history.replaceState({}, '', '/breeder/litters')
      }
    }
  }, [litters])

  // Handle form submission
  const handleFormSubmit = async (formData: any) => {
    try {
      const url = editingLitter ? `/api/litters/${editingLitter.id}` : '/api/litters'
      const method = editingLitter ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        await loadLitters()
        setShowForm(false)
        setEditingLitter(null)
        setError(null)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to save litter')
      }
    } catch (error) {
      setError('Failed to save litter')
      console.error('Save litter error:', error)
    }
  }

  // Handle edit
  const handleEdit = (litter: Litter) => {
    setEditingLitter(litter)
    setShowForm(true)
    setError(null)
  }

  // Handle delete
  const handleDelete = async (litter: Litter) => {
    if (!confirm(`Are you sure you want to delete litter "${litter.litterCode}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/litters/${litter.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await loadLitters()
        setError(null)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to delete litter')
      }
    } catch (error) {
      setError('Failed to delete litter')
      console.error('Delete litter error:', error)
    }
  }

  // Handle view
  const handleView = (litter: Litter) => {
    window.location.href = `/breeder/litters/${litter.id}`
  }

  // Handle cancel form
  const handleCancel = () => {
    setShowForm(false)
    setEditingLitter(null)
    setError(null)
  }

  // Handle add new litter
  const handleAddNew = () => {
    setEditingLitter(null)
    setShowForm(true)
    setError(null)
  }

  // Convert litter data for form
  const getFormData = (litter: Litter | null) => {
    if (!litter) return undefined

    return {
      litterCode: litter.litterCode,
      minExpectedSizeLbs: litter.minExpectedSizeLbs?.toString() || '',
      maxExpectedSizeLbs: litter.maxExpectedSizeLbs?.toString() || '',
      expectedBirthDate: litter.expectedBirthDate ? new Date(litter.expectedBirthDate).toISOString().split('T')[0] : '',
      actualBirthDate: litter.actualBirthDate ? new Date(litter.actualBirthDate).toISOString().split('T')[0] : '',
      status: litter.status,
      color: litter.color || '',
      momId: litter.momId?.toString() || '',
      dadId: litter.dadId?.toString() || '',
    }
  }

  return (
    <DashboardLayout userType="breeder" title="Litter Management">
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {showForm ? (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {editingLitter ? 'Edit Litter' : 'Add New Litter'}
            </h2>
          </div>
          <LitterForm
            initialData={getFormData(editingLitter)}
            onSubmit={handleFormSubmit}
            onCancel={handleCancel}
            submitLabel={editingLitter ? 'Update Litter' : 'Create Litter'}
            litterId={editingLitter?.id}
          />
        </div>
      ) : (
        <div>
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Your Litters</h2>
              <p className="text-gray-600">Manage your breeding litters and track their progress.</p>
            </div>
            <Button onClick={handleAddNew}>
              Add New Litter
            </Button>
          </div>

          <LitterList
            litters={litters}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onView={handleView}
            loading={loading}
          />
        </div>
      )}
    </DashboardLayout>
  )
}
