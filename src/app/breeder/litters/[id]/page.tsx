'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Button } from '@/components/ui/Button'
import { BulkPriceUpdate } from '@/components/litters/BulkPriceUpdate'

interface Litter {
  id: number
  litterCode: string
  minExpectedSizeLbs?: number
  maxExpectedSizeLbs?: number
  expectedBirthDate?: Date
  actualBirthDate?: Date
  status: 'NOT_BORN' | 'BORN' | 'READY_TO_GO_HOME'
  color?: string
  momId?: number
  dadId?: number
  createdAt: Date
  updatedAt: Date
}

interface Puppy {
  id: number
  name: string
  description?: string
  birthDate?: Date
  color?: string
  price?: number
  ownerId?: number
  ownerType?: 'breeder' | 'customer'
}

interface BreedingDog {
  id: number
  name: string
}

interface Photo {
  id: number
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  createdAt: Date
}

interface LitterDetailPageProps {
  params: Promise<{ id: string }>
}

export default function LitterDetailPage({ params }: LitterDetailPageProps) {
  const router = useRouter()
  const [litter, setLitter] = useState<Litter | null>(null)
  const [puppies, setPuppies] = useState<Puppy[]>([])
  const [breedingDogs, setBreedingDogs] = useState<BreedingDog[]>([])
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [litterId, setLitterId] = useState<string | null>(null)
  const [showBulkPriceUpdate, setShowBulkPriceUpdate] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  // Resolve params
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params
      setLitterId(resolvedParams.id)
    }
    resolveParams()
  }, [params])

  // Load litter details
  useEffect(() => {
    if (!litterId) return

    const loadLitterDetails = async () => {
      try {
        setLoading(true)
        
        // Load litter data
        const litterResponse = await fetch(`/api/litters/${litterId}`)
        if (!litterResponse.ok) {
          throw new Error('Failed to load litter')
        }
        const litterData = await litterResponse.json()
        setLitter(litterData.litter)

        // Load puppies for this litter
        const puppiesResponse = await fetch(`/api/puppies?litterId=${litterId}`)
        if (puppiesResponse.ok) {
          const puppiesData = await puppiesResponse.json()
          setPuppies(puppiesData.puppies || [])
        }

        // Load breeding dogs for parent names
        const breedingDogsResponse = await fetch('/api/breeding-dogs')
        if (breedingDogsResponse.ok) {
          const breedingDogsData = await breedingDogsResponse.json()
          setBreedingDogs(breedingDogsData.breedingDogs || [])
        }

        // Load photos for this litter
        const photosResponse = await fetch(`/api/photos?entityType=litter&entityId=${litterId}`)
        if (photosResponse.ok) {
          const photosData = await photosResponse.json()
          setPhotos(photosData.photos || [])
        }

      } catch (error) {
        setError('Failed to load litter details')
        console.error('Load litter details error:', error)
      } finally {
        setLoading(false)
      }
    }

    loadLitterDetails()
  }, [litterId])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'NOT_BORN': { label: 'Not Born', className: 'bg-yellow-100 text-yellow-800' },
      'BORN': { label: 'Born', className: 'bg-blue-100 text-blue-800' },
      'READY_TO_GO_HOME': { label: 'Ready to Go Home', className: 'bg-green-100 text-green-800' },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, className: 'bg-gray-100 text-gray-800' }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Not set'
    return new Date(date).toLocaleDateString()
  }

  const formatSizeRange = (min?: number, max?: number) => {
    if (!min && !max) return 'Not specified'
    if (min && max) return `${min} - ${max} lbs`
    if (min) return `${min}+ lbs`
    if (max) return `Up to ${max} lbs`
    return 'Not specified'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const handleBulkPriceUpdate = async (price: number) => {
    if (!litterId) return

    try {
      const response = await fetch(`/api/litters/${litterId}/update-prices`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ price }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update prices')
      }

      setMessage({ type: 'success', text: `Updated prices for ${result.updatedCount} puppies` })
      setShowBulkPriceUpdate(false)

      // Update the puppies state with new prices
      setPuppies(result.puppies || [])

      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: any) {
      console.error('Bulk price update error:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to update prices' })
    }
  }

  const getParentName = (parentId?: number) => {
    if (!parentId) return 'Not specified'
    const parent = breedingDogs.find(dog => dog.id === parentId)
    return parent ? parent.name : `Dog #${parentId}`
  }

  const handleEdit = () => {
    router.push(`/breeder/litters?edit=${litterId}`)
  }

  const handleBack = () => {
    router.push('/breeder/litters')
  }

  if (loading) {
    return (
      <DashboardLayout userType="breeder" title="Litter Details">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !litter) {
    return (
      <DashboardLayout userType="breeder" title="Litter Details">
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error || 'Litter not found'}</div>
          <Button onClick={handleBack}>Back to Litters</Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userType="breeder" title={`Litter: ${litter.litterCode}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{litter.litterCode}</h2>
            <p className="text-gray-600">Litter details and puppy information</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleBack}>
              Back to Litters
            </Button>
            <Button onClick={handleEdit}>
              Edit Litter
            </Button>
          </div>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`rounded-md p-4 ${
            message.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  message.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {message.text}
                </p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setMessage(null)}
                    className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      message.type === 'success'
                        ? 'text-green-500 hover:bg-green-100 focus:ring-green-600'
                        : 'text-red-500 hover:bg-red-100 focus:ring-red-600'
                    }`}
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Price Update Form */}
        {showBulkPriceUpdate && (
          <BulkPriceUpdate
            litterId={parseInt(litterId!)}
            currentPuppyCount={puppies.length}
            onUpdate={handleBulkPriceUpdate}
            onCancel={() => setShowBulkPriceUpdate(false)}
            loading={loading}
          />
        )}

        {/* Litter Information */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Litter Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">{getStatusBadge(litter.status)}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Color</label>
              <p className="mt-1 text-sm text-gray-900">{litter.color || 'Not specified'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Expected Size</label>
              <p className="mt-1 text-sm text-gray-900">{formatSizeRange(litter.minExpectedSizeLbs, litter.maxExpectedSizeLbs)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Expected Birth Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(litter.expectedBirthDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Actual Birth Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(litter.actualBirthDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Created</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(litter.createdAt)}</p>
            </div>
          </div>
        </div>

        {/* Parent Information */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Parent Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500">Mother</label>
              <p className="mt-1 text-sm text-gray-900">{getParentName(litter.momId)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Father</label>
              <p className="mt-1 text-sm text-gray-900">{getParentName(litter.dadId)}</p>
            </div>
          </div>
        </div>

        {/* Photo Gallery */}
        {photos.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Litter Photos ({photos.length})</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {photos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <img
                    src={photo.url}
                    alt={photo.originalName}
                    className="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                    onClick={() => window.open(photo.url, '_blank')}
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                    <p className="truncate">{photo.originalName}</p>
                    <p>{new Date(photo.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Puppies */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Puppies ({puppies.length})</h3>
            <div className="flex space-x-2">
              {puppies.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBulkPriceUpdate(true)}
                >
                  Update All Prices
                </Button>
              )}
              <Button
                size="sm"
                onClick={() => router.push(`/breeder/puppies?litterId=${litter.id}`)}
              >
                Add Puppy
              </Button>
            </div>
          </div>
          
          {puppies.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <p className="text-gray-500">No puppies in this litter yet</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {puppies.map((puppy) => (
                <div key={puppy.id} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900">{puppy.name}</h4>
                  {puppy.color && (
                    <p className="text-sm text-gray-600">Color: {puppy.color}</p>
                  )}
                  {puppy.birthDate && (
                    <p className="text-sm text-gray-600">Born: {formatDate(puppy.birthDate)}</p>
                  )}
                  {puppy.price && (
                    <p className="text-sm font-medium text-green-600">Price: {formatCurrency(puppy.price)}</p>
                  )}
                  {!puppy.price && (
                    <p className="text-sm text-gray-500">Price: Not set</p>
                  )}
                  {puppy.ownerType && (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2 ${
                      puppy.ownerType === 'customer' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                    }`}>
                      {puppy.ownerType === 'customer' ? 'Sold' : 'Available'}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
