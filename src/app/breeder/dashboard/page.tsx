'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Button } from '@/components/ui/Button'
import { UpcomingEvents, Event } from '@/components/calendar/UpcomingEvents'
import { CalendarView } from '@/components/calendar/CalendarView'
import { AddEventForm, EventFormData } from '@/components/calendar/AddEventForm'
import { EventDetailModal } from '@/components/calendar/EventDetailModal'
import { useUser } from '@/contexts/UserContext'
import { Breeder } from '@/lib/breeders'
import { LogoUpload } from '@/components/breeder/LogoUpload'

export default function BreederDashboard() {
  const { user } = useUser()
  const [breeder, setBreeder] = useState<Breeder | null>(null)
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingEvent, setEditingEvent] = useState<Event | null>(null)
  const [viewingEvent, setViewingEvent] = useState<Event | null>(null)
  const [selectedDate, setSelectedDate] = useState<string>('')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    loadEvents()
    if (user?.userType === 'breeder') {
      loadBreederInfo()
    }
  }, [user])

  const loadBreederInfo = async () => {
    try {
      const response = await fetch('/api/breeders/me')
      if (response.ok) {
        const data = await response.json()
        setBreeder(data.breeder)
      }
    } catch (error) {
      console.error('Load breeder info error:', error)
    }
  }

  const handleLogoUpdate = (logoUrl: string | null) => {
    if (breeder) {
      setBreeder({
        ...breeder,
        logoUrl: logoUrl || undefined
      })
    }
  }

  const loadEvents = async () => {
    try {
      setLoading(true)

      // Get events for the next 30 days
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      const response = await fetch(`/api/events?startDate=${startDate}&endDate=${endDate}&limit=10`)

      if (!response.ok) {
        throw new Error('Failed to load events')
      }

      const data = await response.json()
      setEvents(data.events || [])
    } catch (error) {
      console.error('Load events error:', error)
      setMessage({ type: 'error', text: 'Failed to load events' })
    } finally {
      setLoading(false)
    }
  }

  const handleAddEvent = async (data: EventFormData) => {
    try {
      const isEditing = !!editingEvent
      const url = isEditing ? `/api/events/${editingEvent.id}` : '/api/events'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `Failed to ${isEditing ? 'update' : 'add'} event`)
      }

      setMessage({
        type: 'success',
        text: `Event ${isEditing ? 'updated' : 'added'} successfully!`
      })
      setShowAddForm(false)
      setEditingEvent(null)
      setSelectedDate('')
      loadEvents()

      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: any) {
      console.error('Save event error:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to save event' })
    }
  }

  const handleDateClick = (date: string) => {
    setSelectedDate(date)
    setShowAddForm(true)
  }

  const handleEventClick = (event: Event) => {
    setViewingEvent(event)
  }

  const handleViewEvent = (event: Event) => {
    setViewingEvent(event)
  }

  const handleEditEvent = (event: Event) => {
    setEditingEvent(event)
    setShowAddForm(true)
    setViewingEvent(null)
  }

  const handleEditFromView = () => {
    if (viewingEvent) {
      setEditingEvent(viewingEvent)
      setShowAddForm(true)
      setViewingEvent(null)
    }
  }

  const handleCloseView = () => {
    setViewingEvent(null)
  }

  const handleDeleteFromView = async () => {
    if (viewingEvent) {
      await handleDeleteEvent(viewingEvent)
      setViewingEvent(null)
    }
  }

  const handleDeleteEvent = async (event: Event) => {
    if (!confirm(`Are you sure you want to delete "${event.title}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/events/${event.id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete event')
      }

      setMessage({ type: 'success', text: 'Event deleted successfully!' })
      loadEvents()

      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: any) {
      console.error('Delete event error:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to delete event' })
    }
  }
  return (
    <DashboardLayout userType="breeder">
      <div>
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Breeder Administration Dashboard
          </h2>
          <p className="text-gray-600 text-lg">
            Manage your breeding operations, track litters, handle customer inquiries, 
            and maintain comprehensive breeding records.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Litters</p>
                <p className="text-2xl font-semibold text-gray-900">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Available Puppies</p>
                <p className="text-2xl font-semibold text-gray-900">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Customer Inquiries</p>
                <p className="text-2xl font-semibold text-gray-900">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Sales</p>
                <p className="text-2xl font-semibold text-gray-900">$0</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Manage Litters</h3>
              <p className="text-gray-500 mb-4">
                Register a new litter and track breeding records
              </p>
              <Button
                className="w-full"
                onClick={() => window.location.href = '/breeder/litters'}
              >
                Manage Litters
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Manage Puppies</h3>
              <p className="text-gray-500 mb-4">
                Create and manage puppy profiles and ownership
              </p>
              <Button
                className="w-full"
                onClick={() => window.location.href = '/breeder/puppies'}
              >
                Manage Puppies
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Customer Messages</h3>
              <p className="text-gray-500 mb-4">
                Respond to customer inquiries and messages
              </p>
              <Button className="w-full">
                View Messages
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Business Profile</h3>
              <p className="text-gray-500 mb-4">
                Update your business information and settings
              </p>
              <Button variant="outline" className="w-full">
                Edit Profile
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Sales Reports</h3>
              <p className="text-gray-500 mb-4">
                View detailed sales analytics and reports
              </p>
              <Button variant="outline" className="w-full">
                View Reports
              </Button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Settings</h3>
              <p className="text-gray-500 mb-4">
                Configure system preferences and notifications
              </p>
              <Button variant="outline" className="w-full">
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Public Website Section */}
        {breeder?.breederCode && (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-8 border border-green-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Your Public Website</h3>
                  <p className="text-gray-600 mb-2">
                    Share your professional breeder website with potential customers
                  </p>
                  <div className="flex items-center space-x-2">
                    <code className="bg-white px-3 py-1 rounded border text-sm text-gray-800">
                      {window.location.origin}/{breeder.breederCode}
                    </code>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(`${window.location.origin}/${breeder.breederCode}`)
                        setMessage({ type: 'success', text: 'Website URL copied to clipboard!' })
                        setTimeout(() => setMessage(null), 3000)
                      }}
                      className="text-green-600 hover:text-green-700 p-1"
                      title="Copy URL"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => window.open(`/${breeder.breederCode}`, '_blank')}
                  className="bg-white"
                >
                  Preview Website
                </Button>
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(`${window.location.origin}/${breeder.breederCode}`)
                    setMessage({ type: 'success', text: 'Website URL copied to clipboard!' })
                    setTimeout(() => setMessage(null), 3000)
                  }}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Share Website
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/breeder/business-profile'}
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"
                >
                  Edit Business Profile
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Logo Management Section */}
        {breeder && (
          <div className="bg-white rounded-lg p-6 mb-8 border border-gray-200">
            <LogoUpload
              currentLogoUrl={breeder.logoUrl}
              onLogoUpdate={handleLogoUpdate}
              businessName={breeder.businessName}
            />
          </div>
        )}

        {/* Success/Error Messages */}
        {message && (
          <div className={`rounded-md p-4 mb-6 ${
            message.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  message.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {message.text}
                </p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setMessage(null)}
                    className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      message.type === 'success'
                        ? 'text-green-500 hover:bg-green-100 focus:ring-green-600'
                        : 'text-red-500 hover:bg-red-100 focus:ring-red-600'
                    }`}
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add/Edit Event Form */}
        {showAddForm && (
          <div className="mb-6">
            <AddEventForm
              onSubmit={handleAddEvent}
              onCancel={() => {
                setShowAddForm(false)
                setEditingEvent(null)
                setSelectedDate('')
              }}
              loading={loading}
              initialDate={selectedDate}
              initialData={editingEvent ? {
                title: editingEvent.title,
                description: editingEvent.description,
                eventDate: new Date(editingEvent.eventDate).toISOString().split('T')[0],
                eventTime: editingEvent.eventTime,
                eventType: editingEvent.eventType,
                location: editingEvent.location,
                allDay: editingEvent.allDay,
                reminderMinutes: editingEvent.reminderMinutes,
              } : undefined}
              isEditing={!!editingEvent}
            />
          </div>
        )}

        {/* Calendar and Events */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <CalendarView
              events={events}
              onDateClick={handleDateClick}
              onEventClick={handleEventClick}
            />
          </div>

          <div>
            <UpcomingEvents
              events={events}
              loading={loading}
              onAddEvent={() => setShowAddForm(true)}
              onViewEvent={handleViewEvent}
              onEditEvent={handleEditEvent}
              onDeleteEvent={handleDeleteEvent}
            />
          </div>
        </div>

        {/* Event Detail Modal */}
        {viewingEvent && (
          <EventDetailModal
            event={viewingEvent}
            onClose={handleCloseView}
            onEdit={handleEditFromView}
            onDelete={handleDeleteFromView}
          />
        )}
      </div>
    </DashboardLayout>
  )
}
