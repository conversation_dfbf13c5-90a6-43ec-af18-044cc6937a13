'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { CustomerList } from '@/components/customers/CustomerList'
import { AddCustomerForm, CustomerFormData } from '@/components/customers/AddCustomerForm'
import { EditCustomerForm } from '@/components/customers/EditCustomerForm'
import { CustomerDetailModal } from '@/components/customers/CustomerDetailModal'
import { Button } from '@/components/ui/Button'

interface Customer {
  relationshipId: number
  customerId: number
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    aptNumber?: string
    city: string
    state: string
    zipCode: string
  }
  relationshipType: 'owner' | 'lead'
  status: 'active' | 'inactive'
  notes: string
  customerCreatedAt: Date
  relationshipCreatedAt: Date
  relationshipUpdatedAt: Date
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [viewingCustomer, setViewingCustomer] = useState<Customer | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    relationshipType: '',
    status: 'active',
    sortBy: 'relationshipCreatedAt',
    sortOrder: 'desc' as 'asc' | 'desc',
    page: 1
  })
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    loadCustomers()
  }, [filters])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        search: filters.search,
        relationshipType: filters.relationshipType,
        status: filters.status,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        page: filters.page.toString(),
        limit: '20'
      })

      const response = await fetch(`/api/customers?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to load customers')
      }

      const data = await response.json()
      setCustomers(data.customers || [])
      setPagination(data.pagination)
    } catch (error) {
      console.error('Load customers error:', error)
      setMessage({ type: 'error', text: 'Failed to load customers' })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }))
  }

  const handleFilter = (newFilters: { relationshipType: string; status: string }) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }))
  }

  const handleSort = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sortBy, sortOrder, page: 1 }))
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handleAddCustomer = async (data: CustomerFormData) => {
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add customer')
      }

      setMessage({ type: 'success', text: 'Customer added successfully!' })
      setShowAddForm(false)
      loadCustomers()
      
      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: unknown) {
      console.error('Add customer error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to add customer'
      setMessage({ type: 'error', text: errorMessage })
    }
  }

  const handleView = (customer: Customer) => {
    setViewingCustomer(customer)
    setShowAddForm(false)
    setEditingCustomer(null)
  }

  const handleCloseView = () => {
    setViewingCustomer(null)
  }

  const handleEditFromView = () => {
    if (viewingCustomer) {
      setEditingCustomer(viewingCustomer)
      setViewingCustomer(null)
    }
  }

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer)
    setShowAddForm(false)
    setViewingCustomer(null)
  }

  const handleUpdateCustomer = async (data: {
    relationshipType: string;
    status: string;
    notes: string;
    firstName: string;
    lastName: string;
    phone: string;
    streetAddress: string;
    aptNumber: string;
    city: string;
    state: string;
    zipCode: string;
  }) => {
    if (!editingCustomer) return

    try {
      const response = await fetch(`/api/customers/${editingCustomer.relationshipId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update customer')
      }

      setMessage({ type: 'success', text: 'Customer relationship updated successfully!' })
      setEditingCustomer(null)
      loadCustomers()

      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: unknown) {
      console.error('Update customer error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update customer'
      setMessage({ type: 'error', text: errorMessage })
    }
  }

  const handleCancelEdit = () => {
    setEditingCustomer(null)
  }

  const handleDelete = async (customer: Customer) => {
    if (!confirm(`Are you sure you want to remove ${customer.firstName} ${customer.lastName} from your customer list?`)) {
      return
    }

    try {
      const response = await fetch(`/api/customers/${customer.relationshipId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove customer')
      }

      setMessage({ type: 'success', text: 'Customer removed successfully!' })
      loadCustomers()
      
      // Clear success message after 5 seconds
      setTimeout(() => setMessage(null), 5000)

    } catch (error: unknown) {
      console.error('Delete customer error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove customer'
      setMessage({ type: 'error', text: errorMessage })
    }
  }

  return (
    <DashboardLayout userType="breeder" title="Customer Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Customers</h2>
            <p className="text-gray-600">Manage your customer relationships and leads</p>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            Add Customer
          </Button>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`rounded-md p-4 ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200' 
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  message.type === 'success' ? 'text-green-800' : 'text-red-800'
                }`}>
                  {message.text}
                </p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => setMessage(null)}
                    className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      message.type === 'success'
                        ? 'text-green-500 hover:bg-green-100 focus:ring-green-600'
                        : 'text-red-500 hover:bg-red-100 focus:ring-red-600'
                    }`}
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Customer Form */}
        {showAddForm && (
          <AddCustomerForm
            onSubmit={handleAddCustomer}
            onCancel={() => setShowAddForm(false)}
            loading={loading}
          />
        )}

        {/* Edit Customer Form */}
        {editingCustomer && (
          <EditCustomerForm
            customer={editingCustomer}
            onSubmit={handleUpdateCustomer}
            onCancel={handleCancelEdit}
            loading={loading}
          />
        )}

        {/* Customer List */}
        {!showAddForm && !editingCustomer && (
          <CustomerList
            customers={customers}
            pagination={pagination}
            loading={loading}
            onSearch={handleSearch}
            onFilter={handleFilter}
            onSort={handleSort}
            onPageChange={handlePageChange}
            onView={handleView}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        )}

        {/* Customer Detail Modal */}
        {viewingCustomer && (
          <CustomerDetailModal
            customer={viewingCustomer}
            onClose={handleCloseView}
            onEdit={handleEditFromView}
          />
        )}
      </div>
    </DashboardLayout>
  )
}
