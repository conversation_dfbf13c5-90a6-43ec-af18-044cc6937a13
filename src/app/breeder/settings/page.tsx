'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { LogoUpload } from '@/components/breeder/LogoUpload'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import { useUser } from '@/contexts/UserContext'
import { Breeder } from '@/lib/breeders'

const US_STATES = [
  { value: '', label: 'Select State' },
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
]

export default function BreederSettingsPage() {
  const { user } = useUser()
  const [breeder, setBreeder] = useState<Breeder | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    if (user) {
      loadBreederInfo()
    }
  }, [user])

  const loadBreederInfo = async () => {
    try {
      const response = await fetch('/api/breeders/me')
      if (response.ok) {
        const data = await response.json()
        setBreeder(data.breeder)
      }
    } catch (error) {
      console.error('Load breeder info error:', error)
      setMessage({ type: 'error', text: 'Failed to load breeder information' })
    } finally {
      setLoading(false)
    }
  }

  const handleLogoUpdate = (logoUrl: string | null) => {
    if (breeder) {
      setBreeder({
        ...breeder,
        logoUrl: logoUrl || undefined
      })
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    if (breeder) {
      setBreeder({
        ...breeder,
        [name]: value
      })
    }
  }

  const handleSaveBusinessInfo = async () => {
    if (!breeder) return

    try {
      setSaving(true)
      setMessage(null)

      const response = await fetch('/api/breeders/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: breeder.businessName,
          businessPhone: breeder.businessPhone,
          businessStreetAddress: breeder.businessStreetAddress,
          businessAptNumber: breeder.businessAptNumber,
          businessCity: breeder.businessCity,
          businessState: breeder.businessState,
          businessZipCode: breeder.businessZipCode,
          website: breeder.website,
          description: breeder.description,
          specialties: breeder.specialties,
          yearsExperience: breeder.yearsExperience,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update business information')
      }

      const data = await response.json()
      setBreeder(data.breeder)
      setMessage({ type: 'success', text: 'Business information updated successfully!' })
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000)

    } catch (error) {
      console.error('Save business info error:', error)
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to update business information' 
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout userType="breeder">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (!breeder) {
    return (
      <DashboardLayout userType="breeder">
        <div className="text-center py-12">
          <p className="text-gray-500">Breeder information not found.</p>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userType="breeder">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Breeder Settings</h1>
          <p className="mt-2 text-gray-600">
            Manage your business information, logo, and public profile settings.
          </p>
        </div>

        {/* Logo Management Section */}
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <LogoUpload
            currentLogoUrl={breeder.logoUrl}
            onLogoUpdate={handleLogoUpdate}
            businessName={breeder.businessName}
          />
        </div>

        {/* Business Information Section */}
        <div className="bg-white rounded-lg p-6 border border-gray-200">
          <h2 className="text-lg font-medium text-gray-900 mb-6">Business Information</h2>
          
          <div className="space-y-6">
            {/* Basic Business Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Business Name"
                name="businessName"
                value={breeder.businessName || ''}
                onChange={handleInputChange}
                placeholder="ABC Dog Breeding"
              />

              <Input
                label="Business Phone"
                name="businessPhone"
                type="tel"
                value={breeder.businessPhone || ''}
                onChange={handleInputChange}
                placeholder="(*************"
              />
            </div>

            {/* Business Address */}
            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-700">Business Address</h3>
              
              <Input
                label="Street Address"
                name="businessStreetAddress"
                value={breeder.businessStreetAddress || ''}
                onChange={handleInputChange}
                placeholder="123 Business Street"
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Input
                  label="Apartment/Unit Number"
                  name="businessAptNumber"
                  value={breeder.businessAptNumber || ''}
                  onChange={handleInputChange}
                  placeholder="Suite 100"
                />

                <Input
                  label="City"
                  name="businessCity"
                  value={breeder.businessCity || ''}
                  onChange={handleInputChange}
                  placeholder="Business City"
                />

                <div className="grid grid-cols-2 gap-3">
                  <Select
                    label="State"
                    name="businessState"
                    value={breeder.businessState || ''}
                    onChange={handleInputChange}
                    options={US_STATES}
                  />

                  <Input
                    label="ZIP Code"
                    name="businessZipCode"
                    value={breeder.businessZipCode || ''}
                    onChange={handleInputChange}
                    placeholder="12345"
                  />
                </div>
              </div>
            </div>

            {/* Additional Business Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Website"
                name="website"
                type="url"
                value={breeder.website || ''}
                onChange={handleInputChange}
                placeholder="https://www.yourbusiness.com"
              />

              <Input
                label="Years of Experience"
                name="yearsExperience"
                type="number"
                value={breeder.yearsExperience?.toString() || ''}
                onChange={handleInputChange}
                placeholder="5"
              />
            </div>

            <Input
              label="Specialties"
              name="specialties"
              value={breeder.specialties || ''}
              onChange={handleInputChange}
              placeholder="Golden Retrievers, Labrador Retrievers"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Description
              </label>
              <textarea
                name="description"
                value={breeder.description || ''}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Tell customers about your breeding program, experience, and what makes your dogs special..."
              />
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <Button
                onClick={handleSaveBusinessInfo}
                disabled={saving}
                className="px-6 py-2"
              >
                {saving ? 'Saving...' : 'Save Business Information'}
              </Button>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
