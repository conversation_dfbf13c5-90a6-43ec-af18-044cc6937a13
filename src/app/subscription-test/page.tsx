'use client'

import { useState, useEffect } from 'react'

export default function SubscriptionTestPage() {
  const [status, setStatus] = useState('Loading...')
  const [error, setError] = useState('')

  useEffect(() => {
    testSubscriptionSystem()
  }, [])

  const testSubscriptionSystem = async () => {
    try {
      // Test 1: Check if subscription API is accessible
      setStatus('Testing subscription API...')
      const response = await fetch('/api/subscriptions/current')
      
      if (response.status === 401) {
        setStatus('✅ Subscription API is working (unauthorized as expected)')
      } else if (response.ok) {
        const data = await response.json()
        setStatus('✅ Subscription API is working and returned data')
      } else {
        setError(`❌ Subscription API returned status: ${response.status}`)
        return
      }

      // Test 2: Check if subscription plans are seeded
      setStatus('Testing subscription plans...')
      const plansResponse = await fetch('/api/seed-plans', { method: 'POST' })
      
      if (plansResponse.ok) {
        setStatus('✅ All tests passed! Subscription system is working.')
      } else {
        setError('❌ Failed to seed subscription plans')
      }

    } catch (err) {
      setError(`❌ Error: ${err}`)
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Subscription System Test</h1>
      
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">System Status</h2>
        
        {error ? (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <p className="text-red-700">{error}</p>
          </div>
        ) : (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <p className="text-green-700">{status}</p>
          </div>
        )}

        <div className="mt-6">
          <button
            onClick={testSubscriptionSystem}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Run Test Again
          </button>
        </div>
      </div>

      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
        <div className="space-y-2">
          <a 
            href="/breeder/settings" 
            className="block text-blue-600 hover:text-blue-800"
          >
            → Breeder Settings (with Subscription Manager)
          </a>
          <a 
            href="/test-subscription" 
            className="block text-blue-600 hover:text-blue-800"
          >
            → Subscription API Test Page
          </a>
        </div>
      </div>

      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Implementation Status</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>Database tables created</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>Subscription plans seeded</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>Stripe integration configured</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>API routes implemented</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>Frontend components created</span>
          </div>
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span>Usage limit enforcement</span>
          </div>
        </div>
      </div>
    </div>
  )
}
