import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Get session cookie
  const sessionCookie = request.cookies.get('pawledger_session')
  
  // Parse session data if it exists
  let sessionData = null
  if (sessionCookie?.value) {
    try {
      sessionData = JSON.parse(sessionCookie.value)
    } catch (error) {
      // Invalid session cookie, clear it
      const response = NextResponse.redirect(new URL('/', request.url))
      response.cookies.delete('pawledger_session')
      return response
    }
  }

  // Protected routes that require authentication
  const protectedRoutes = ['/customer', '/breeder']
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))

  // If accessing a protected route without authentication
  if (isProtectedRoute && !sessionData) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  // If authenticated user tries to access the home page, redirect to their dashboard
  if (pathname === '/' && sessionData) {
    if (sessionData.userType === 'breeder') {
      return NextResponse.redirect(new URL('/breeder/dashboard', request.url))
    } else if (sessionData.userType === 'customer') {
      return NextResponse.redirect(new URL('/customer/dashboard', request.url))
    }
  }

  // Role-based route protection
  if (sessionData) {
    // Prevent customers from accessing breeder routes
    if (pathname.startsWith('/breeder') && sessionData.userType !== 'breeder') {
      return NextResponse.redirect(new URL('/customer/dashboard', request.url))
    }
    
    // Prevent breeders from accessing customer routes
    if (pathname.startsWith('/customer') && sessionData.userType !== 'customer') {
      return NextResponse.redirect(new URL('/breeder/dashboard', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)',
  ],
}
