// Script to test 3-step registration for breeders
console.log('Testing 3-step registration flow for breeders...')

async function test3StepRegistration() {
  try {
    console.log('1. Testing customer registration (2-step)...')
    
    const customerData = {
      email: `testcustomer3step${Date.now()}@example.com`,
      password: 'testpass123',
      userType: 'customer',
      firstName: 'Test',
      lastName: 'Customer',
      phone: '(*************',
      streetAddress: '123 Customer St',
      aptNumber: 'Apt 1',
      city: 'Customer City',
      state: 'CA',
      zipCode: '12345'
    }
    
    const customerResponse = await fetch('http://localhost:3003/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(customerData)
    })
    
    console.log(`   Customer registration status: ${customerResponse.status}`)
    
    if (customerResponse.status === 200) {
      console.log('   ✅ Customer registration successful (2-step process)!')
      const customerResult = await customerResponse.json()
      console.log(`   Created customer: ${customerResult.user.firstName} ${customerResult.user.lastName}`)
    } else {
      const errorData = await customerResponse.json()
      console.log('   ❌ Customer registration failed:', errorData.error)
    }
    
    console.log('\n2. Testing breeder registration (3-step)...')
    
    const breederData = {
      email: `testbreeder3step${Date.now()}@example.com`,
      password: 'testpass123',
      userType: 'breeder',
      firstName: 'Test',
      lastName: 'Breeder',
      phone: '(*************',
      // Personal address
      streetAddress: '456 Breeder Ave',
      aptNumber: 'Suite 2',
      city: 'Breeder Town',
      state: 'TX',
      zipCode: '67890',
      // Business information
      businessName: 'Test Breeding Business LLC',
      businessPhone: '(*************',
      // Business address
      businessStreetAddress: '789 Business Blvd',
      businessAptNumber: 'Unit 3',
      businessCity: 'Business City',
      businessState: 'FL',
      businessZipCode: '54321'
    }
    
    const breederResponse = await fetch('http://localhost:3003/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(breederData)
    })
    
    console.log(`   Breeder registration status: ${breederResponse.status}`)
    
    if (breederResponse.status === 200) {
      console.log('   ✅ Breeder registration successful (3-step process)!')
      const breederResult = await breederResponse.json()
      console.log(`   Created breeder: ${breederResult.user.firstName} ${breederResult.user.lastName}`)
      console.log(`   Business: ${breederResult.user.businessName || 'N/A'}`)
    } else {
      const errorData = await breederResponse.json()
      console.log('   ❌ Breeder registration failed:', errorData.error)
    }
    
    console.log('\n3. Testing validation for missing business address...')
    
    const incompleteBreederData = {
      email: `incomplete${Date.now()}@example.com`,
      password: 'testpass123',
      userType: 'breeder',
      firstName: 'Incomplete',
      lastName: 'Breeder',
      streetAddress: '123 Personal St',
      city: 'Personal City',
      state: 'CA',
      zipCode: '12345',
      businessName: 'Test Business',
      // Missing business address fields
    }
    
    const incompleteResponse = await fetch('http://localhost:3003/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(incompleteBreederData)
    })
    
    console.log(`   Incomplete breeder data status: ${incompleteResponse.status}`)
    
    if (incompleteResponse.status === 400) {
      console.log('   ✅ Validation working correctly for incomplete business data')
      const errorData = await incompleteResponse.json()
      console.log(`   Error message: ${errorData.error}`)
    } else {
      console.log('   ❌ Validation not working as expected')
    }
    
    console.log('\n✅ 3-step registration test completed!')
    console.log('\n📋 Registration Flow Summary:')
    console.log('👤 Customers (2-step):')
    console.log('   Step 1: Email, password, account type')
    console.log('   Step 2: Personal info + address → Account created')
    console.log('')
    console.log('🏢 Breeders (3-step):')
    console.log('   Step 1: Email, password, account type')
    console.log('   Step 2: Personal info + personal address')
    console.log('   Step 3: Business info + business address → Account created')
    console.log('')
    console.log('✨ Features:')
    console.log('   🔄 Dynamic progress indicator (2 or 3 steps)')
    console.log('   ⬅️  Back navigation between steps')
    console.log('   ✅ Step-by-step validation')
    console.log('   📱 Mobile-responsive design')
    console.log('   🎯 Separate personal and business addresses for breeders')

  } catch (error) {
    console.error('Test error:', error.message)
  }
}

test3StepRegistration().catch(console.error)
