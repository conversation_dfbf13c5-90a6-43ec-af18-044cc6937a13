# Production Build Error Fixes

## Summary of Remaining Issues

### Critical Errors (Must Fix):
1. **TypeScript `any` types** - Multiple files using `any` instead of proper types
2. **Require imports** - Using `require()` instead of ES6 imports
3. **Unused error variables** - Variables defined but not used

### Warnings (Should Fix):
1. **React Hook dependencies** - Missing dependencies in useEffect
2. **Image elements** - Using `<img>` instead of Next.js `<Image>`

## Fixes Applied So Far:

✅ **Fixed Navigation component**:
- Added missing `key` props for React elements
- Fixed external link handling

✅ **Fixed Auth components**:
- Fixed apostrophe escaping (`Don't` → `Don&apos;t`)
- Added console.error for error handling

✅ **Fixed BulkPriceUpdate component**:
- Removed unused `litterId` parameter
- Updated parent component usage

✅ **Fixed unused imports**:
- Removed unused `useRouter` import
- Commented out unused interface

## Remaining Fixes Needed:

### 1. TypeScript `any` Types
Replace `any` with proper types in:
- `src/app/api/breeders/me/route.ts:81`
- `src/app/api/customers/[id]/route.ts:201,224`
- `src/app/api/events/[id]/route.ts:184`
- `src/app/api/profile/route.ts:143`
- `src/app/breeder/business-profile/page.tsx:130`
- `src/app/breeder/customers/page.tsx:140,206,238`
- `src/app/breeder/dashboard/page.tsx:109,175`
- `src/app/breeder/litters/[id]/page.tsx:186`
- `src/app/breeder/litters/page.tsx:70`
- `src/app/breeder/profile/page.tsx:74`
- `src/app/breeder/puppies/page.tsx:79`
- `src/app/breeder/settings/page.tsx:312`
- `src/app/customer/profile/page.tsx:76`
- `src/contexts/UserContext.tsx:94,124`
- `src/lib/breeders.ts:322`
- `src/lib/database.ts:288,323,333,342,351,360,369,378,387,396,405,414`
- `src/lib/litters.ts:197`
- `src/lib/puppies.ts:217`
- `src/middleware.ts:15`

### 2. Require Imports
Replace `require()` with ES6 imports in:
- `src/app/api/profile/route.ts:131`
- `src/lib/email.ts:51`

### 3. React Hook Dependencies
Add missing dependencies to useEffect in:
- `src/app/[breederCode]/litters/[litterId]/page.tsx:52`
- `src/app/[breederCode]/page.tsx:32`
- `src/app/breeder/customers/page.tsx:68`

### 4. Image Elements
Replace `<img>` with Next.js `<Image>` in:
- `src/app/breeder/litters/[id]/page.tsx:356`
- `src/app/breeder/puppies/[id]/page.tsx:226`
- `src/components/ui/ImageUpload.tsx:216`

### 5. Const vs Let
Fix variable declarations in:
- `src/lib/breeders.ts:90` - Use `const` instead of `let`

## Quick Fix Strategy:

### For `any` types:
```typescript
// Instead of:
} catch (error: any) {

// Use:
} catch (error: unknown) {
  console.error('Error:', error)
```

### For require imports:
```typescript
// Instead of:
const nodemailer = require('nodemailer')

// Use:
import nodemailer from 'nodemailer'
```

### For useEffect dependencies:
```typescript
// Add the function to dependencies or wrap in useCallback
useEffect(() => {
  loadData()
}, [loadData]) // Add missing dependency
```

### For img elements:
```typescript
// Instead of:
<img src={photo.url} alt="Photo" />

// Use:
<Image src={photo.url} alt="Photo" width={200} height={200} />
```

## Build Command:
```bash
npm run build
```

## Deployment Ready Checklist:
- [ ] All TypeScript errors fixed
- [ ] All ESLint errors fixed
- [ ] Build completes successfully
- [ ] Environment variables configured
- [ ] Database schema ready
- [ ] Google Cloud Storage configured
- [ ] Email service configured
